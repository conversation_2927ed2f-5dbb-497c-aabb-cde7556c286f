<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Conversation Section</title>

    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Figtree:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <link rel="stylesheet" href="/project-folder/css/Contact_Us.css">
</head>
<body>
   


    <section id="mainContent" class="container-fluid px-0 ">
        <div class="row justify-content-center">
            <div class="col-11">
                <div class="d-flex flex-column align-items-center py-5">

                    <div class="text-center mb-4">
                        <h2 class="conversation-title mb-2">We're here to help.</h2>
                        <p class="conversation-subtitle mb-5">
                            Have questions, feedback, or need assistance? Our dedicated support team is just a message away and ready to assist you anytime with quick, friendly, and reliable help.
                        </p>
                    </div>
                

                    <div class="conversation-card w-100">
                        <div class="row g-0 h-100">

                            <div class="col-12 col-md-6 d-flex align-items-center justify-content-center character-section">
                                <div class="character-container">
                                    <img src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Dummy_02-93s0m6t4n7729f7RPlGANfjmGwsY8D.svg" alt="Character" class="character-image">
                                </div>
                            </div>
                            

                            <div class="col-12 col-md-6 conversation-content">

                                <div class="vertical-line d-none d-md-block">
                                    <div class="line-brown"></div>
                                    <div class="line-grey"></div>
                                </div>
                                
                                <div class="content-wrapper">
                                    <p class="conversation-tag mb-3">Let's Talk. Your Idea Deserves a Great Start!</p>
                                    <h3 class="conversation-heading mb-4">Start The Conversation</h3>
                                    <button class="btn conversation-btn mb-4" data-bs-toggle="modal" data-bs-target="#contactModal">
                                        <span>Let's Connect</span>
                                        <img src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/leftArrow-d8DhQwLPVk1Bi0Jkvf2qNekR8xpsZ3.svg" alt="Arrow" class="btn-arrow">
                                    </button>
                                    <div class="conversation-taglines">
                                        <div>You Imagine. We Build.</div>
                                        <div>Real Talk. Real Tech.</div>
                                        <div>Code Meets Vision.</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>


    <div class="modal fade" id="contactModal" tabindex="-1" aria-labelledby="contactModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content contact-modal">
                <div class="modal-header border-0">
                    <h5 class="modal-title contact-modal-title" id="contactModalLabel">Let's Connect</h5>
                    <button type="button" class="btn-close contact-modal-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p class="contact-modal-subtitle mb-4">
                        We'd love to hear from you! Please fill out the form below and we'll get back to you as soon as possible.
                    </p>
                    
                    <form id="contactForm" novalidate>
                        <div class="mb-3">
                            <label for="contactName" class="form-label">Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control contact-input" id="contactName" name="name" required>
                            <div class="invalid-feedback"></div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="contactEmail" class="form-label">Email <span class="text-danger">*</span></label>
                            <input type="email" class="form-control contact-input" id="contactEmail" name="email" required>
                            <div class="invalid-feedback"></div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="contactDescription" class="form-label">Description <span class="text-danger">*</span></label>
                            <textarea class="form-control contact-input" id="contactDescription" name="description" rows="4" placeholder="Tell us about you or how we can help you..." required></textarea>
                            <div class="invalid-feedback"></div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn contact-cancel-btn" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn contact-submit-btn" id="submitBtn">
                        <span class="btn-text">Send Message</span>
                        <div class="btn-spinner d-none">
                            <div class="spinner-border spinner-border-sm me-2" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            Sending...
                        </div>
                    </button>
                </div>
            </div>
        </div>
    </div>


    <div class="toast-container position-fixed top-0 end-0 p-3">
        <div id="successToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header bg-success text-white">
                <strong class="me-auto">Success</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body">
                Thank you for your message!
            </div>
        </div>
    </div>


    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <script src="script.js"></script>
</body>
</html>