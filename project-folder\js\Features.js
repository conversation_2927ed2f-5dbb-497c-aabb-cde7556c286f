// JavaScript for loading functionality
document.addEventListener('DOMContentLoaded', function() {
    const skeleton = document.getElementById('features-skeleton');
    const mainContent = document.getElementById('features-main');
    
    // Show skeleton initially, hide main content
    skeleton.classList.remove('d-none');
    mainContent.classList.add('d-none');
    
    // After 1 second, hide skeleton and show main content
    setTimeout(function() {
        skeleton.classList.add('d-none');
        mainContent.classList.remove('d-none');
    }, 1000);
});