<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Social Media Management Solutions - Flowkar</title>
    
    <!-- Bootstrap 5.3.x CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/project-folder/css/style.css">
</head>
<body>
    <div class="management-features-section" id="solutions-section" style="background-color: rgb(255, 255, 255);">
    <div class="container-fluid" >
        <div class="row justify-content-center">
            <div class="col-12">
                <div class="features-container" style="background-image: url('/project-folder/assets/ManagementFeatures/Background.svg'); background-repeat: no-repeat; background-position: center; background-size: cover; height: 100%; margin-bottom: 60px; margin-top: -65px; width: 100%; padding: 70px 30px; ">
                    <!-- Section Header -->
                    <div class="d-flex flex-column justify-content-center align-items-center mb-4 mb-md-5">
                        <div class="solutions-badge d-flex justify-content-center align-items-center mb-3">
                            <img src="/project-folder/assets/ManagementFeatures/IconHeader.svg" alt="Icon Header" class="header-icon me-2" width="18" height="18">
                            <p class="badge-text mb-0">Solutions</p>
                        </div>
                        <h2 class="section-title-Management text-center mb-4 mb-md-5" style="color: #ffffff;">
                            Social Media Management Solutions - Flowkar
                        </h2>
                    </div>

                    <!-- Features Grid -->
                    <div class="row g-4 mx-2 mx-md-4">
                        <!-- Feature Card 1 -->
                        <div class="col-12 col-md-6 col-lg-4">
                            <div class="feature-card h-100 d-flex flex-column">
                                <div class="feature-icon-container mb-2">
                                    <img src="/project-folder/assets/ManagementFeatures/Icon_01.svg" alt="Plan and Publish" class="feature-card-Management" width="48" height="48">
                                </div>
                                <h3 class="feature-title mb-3">Plan, Publish and Perform Like a Pro</h3>
                                <p class="feature-description mb-4 flex-grow-1">
                                    Join the platform built for creators, teams, and brands to get their work done.
                                </p>
                                <button class="read-more-btn d-flex align-items-center justify-content-between">
                                    <span>Read More</span>
                                    <img src="/project-folder/assets/ManagementFeatures/RightArrow.svg" alt="Right Arrow" class="arrow-icon" width="30" height="30">
                                </button>
                            </div>
                        </div>

                        <!-- Feature Card 2 -->
                        <div class="col-12 col-md-6 col-lg-4">
                            <div class="feature-card h-100 d-flex flex-column">
                                <div class="feature-icon-container mb-2">
                                    <img src="/project-folder/assets/ManagementFeatures/Icon_02.svg" alt="Schedule Posts" class="feature-card-Management" width="48" height="48">
                                </div>
                                <h3 class="feature-title mb-3">Post & Schedule across all social media platforms from a single dashboard</h3>
                                <p class="feature-description mb-4 flex-grow-1">
                                    Manage all your social platforms - your content, your brand.
                                </p>
                                <button class="read-more-btn d-flex align-items-center justify-content-between">
                                    <span>Read More</span>
                                    <img src="/project-folder/assets/ManagementFeatures/RightArrow.svg" alt="Right Arrow" class="arrow-icon" width="30" height="30">
                                </button>
                            </div>
                        </div>

                        <!-- Feature Card 3 -->
                        <div class="col-12 col-md-6 col-lg-4">
                            <div class="feature-card h-100 d-flex flex-column">
                                <div class="feature-icon-container mb-4">
                                    <img src="/project-folder/assets/ManagementFeatures/Icon_03.svg" alt="Marketing Tools" class="feature-card-Management" width="48" height="48">
                                </div>
                                <h3 class="feature-title mb-3">Marketing Tools to help influencers and brands maximize reach and engagement</h3>
                                <p class="feature-description mb-4 flex-grow-1">
                                    Build tools for influencers & brands to grow online reach and boost engagement.
                                </p>
                                <button class="read-more-btn d-flex align-items-center justify-content-between">
                                    <span>Read More</span>
                                    <img src="/project-folder/assets/ManagementFeatures/RightArrow.svg" alt="Right Arrow" class="arrow-icon" width="30" height="30">
                                </button>
                            </div>
                        </div>

                        <!-- Feature Card 4 -->
                        <div class="col-12 col-md-6 col-lg-4">
                            <div class="feature-card h-100 d-flex flex-column">
                                <div class="feature-icon-container mb-4">
                                    <img src="/project-folder/assets/ManagementFeatures/Icon_04.svg" alt="Analytics and Reports" class="feature-card-Management" width="48" height="48">
                                </div>
                                <h3 class="feature-title mb-3">Scheduling & Reports with Performance insights for posts, reels, and audience growth</h3>
                                <p class="feature-description mb-4 flex-grow-1">
                                    Understand your audience, optimize your content, and grow your following.
                                </p>
                                <button class="read-more-btn d-flex align-items-center justify-content-between">
                                    <span>Read More</span>
                                    <img src="/project-folder/assets/ManagementFeatures/RightArrow.svg" alt="Right Arrow" class="arrow-icon" width="30" height="30">
                                </button>
                            </div>
                        </div>

                        <!-- Feature Card 5 -->
                        <div class="col-12 col-md-6 col-lg-4">
                            <div class="feature-card h-100 d-flex flex-column">
                                <div class="feature-icon-container mb-4">
                                    <img src="/project-folder/assets/ManagementFeatures/Icon_05.svg" alt="Team Collaboration" class="feature-card-Management" width="48" height="48">
                                </div>
                                <h3 class="feature-title mb-3">Team Access & Roles for content collaboration and approval</h3>
                                <p class="feature-description mb-4 flex-grow-1">
                                    Manage team roles and permissions for seamless content collaboration.
                                </p>
                                <button class="read-more-btn d-flex align-items-center justify-content-between">
                                    <span>Read More</span>
                                    <img src="/project-folder/assets/ManagementFeatures/RightArrow.svg" alt="Right Arrow" class="arrow-icon" width="30" height="30">
                                </button>
                            </div>
                        </div>

                        <!-- Feature Card 6 -->
                        <div class="col-12 col-md-6 col-lg-4">
                            <div class="feature-card h-100 d-flex flex-column">
                                <div class="feature-icon-container mb-4">
                                    <img src="/project-folder/assets/ManagementFeatures/Icon_06.svg" alt="Message Management" class="feature-card-Management" width="48" height="48">
                                </div>
                                <h3 class="feature-title mb-3">Comment & Message Management for real-time engagement</h3>
                                <p class="feature-description mb-4 flex-grow-1">
                                    Keep track of comments, replies - reply back, and build relationships.
                                </p>
                                <button class="read-more-btn d-flex align-items-center justify-content-between">
                                    <span>Read More</span>
                                    <img src="/project-folder/assets/ManagementFeatures/RightArrow.svg" alt="Right Arrow" class="arrow-icon" width="30" height="30">
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

    <!-- Bootstrap 5.3.x JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
