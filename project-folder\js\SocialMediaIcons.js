// Social Media Icons Carousel JavaScript

class SocialMediaCarousel {
    constructor() {
        this.animationOffset = 0;
        this.roundCount = 0;
        this.isRestarting = false;
        this.spacing = 130;
        this.isMobile = false;
        this.intervalId = null;
        
        // Social media icons data with actual image paths
        this.baseSocialIcons = [
            { 
                platform: 'youtube', 
                alt: 'YouTube', 
                color: '#FF0000', 
                icon: './assets/svg_icon/youtube-icon.svg' 
            },
            { 
                platform: 'reddit', 
                alt: 'Reddit', 
                color: '#FF4500', 
                icon: './assets/svg_icon/youtube-icon.svg' 
            },
            { 
                platform: 'instagram', 
                alt: 'Instagram', 
                color: '#E4405F', 
                icon: './assets/svg_icon/youtube-icon.svg' 
            },
            { 
                platform: 'facebook', 
                alt: 'Facebook', 
                color: '#1877F2', 
                icon: './assets/svg_icon/youtube-icon.svg' 
            },
            { 
                platform: 'twitter', 
                alt: 'X (Twitter)', 
                color: '#000000', 
                icon: './assets/svg_icon/youtube-icon.svg' 
            },
            { 
                platform: 'pinterest', 
                alt: 'Pinterest', 
                color: '#BD081C', 
                icon: './assets/svg_icon/youtube-icon.svg' 
            },
            { 
                platform: 'linkedin', 
                alt: 'LinkedIn', 
                color: '#0A66C2', 
                icon: './assets/svg_icon/youtube-icon.svg' 
            },
            { 
                platform: 'threads', 
                alt: 'Threads', 
                color: '#000000', 
                icon: 'assets/svg_icon/thread.svg' 
            },
            { 
                platform: 'reddit', 
                alt: 'Reddit', 
                color: '#FF4500', 
                icon: 'assets/svg_icon/reddit.svg' 
            },
            { 
                platform: 'youtube', 
                alt: 'YouTube', 
                color: '#FF0000', 
                icon: 'assets/svg_icon/youtube-icon.svg' 
            },
            { 
                platform: 'facebook', 
                alt: 'Facebook', 
                color: '#1877F2', 
                icon: 'assets/svg_icon/facebook-icon.svg' 
            },
            { 
                platform: 'instagram', 
                alt: 'Instagram', 
                color: '#E4405F', 
                icon: 'assets/svg_icon/instagram.svg' 
            },
            { 
                platform: 'threads', 
                alt: 'Threads', 
                color: '#000000', 
                icon: 'assets/svg_icon/thread.svg' 
            },
            { 
                platform: 'pinterest', 
                alt: 'Pinterest', 
                color: '#BD081C', 
                icon: 'assets/svg_icon/pintrest-icon.svg' 
            },
            { 
                platform: 'twitter', 
                alt: 'X (Twitter)', 
                color: '#000000', 
                icon: 'assets/svg_icon/twitter-icon.svg' 
            },
            { 
                platform: 'linkedin', 
                alt: 'LinkedIn', 
                color: '#0A66C2', 
                icon: 'assets/svg_icon/linkdin-icon.svg' 
            },
            { 
                platform: 'instagram', 
                alt: 'Instagram', 
                color: '#E4405F', 
                icon: 'assets/svg_icon/instagram.svg' 
            },
            { 
                platform: 'reddit', 
                alt: 'Reddit', 
                color: '#FF4500', 
                icon: 'assets/svg_icon/reddit.svg' 
            },
            { 
                platform: 'pinterest', 
                alt: 'Pinterest', 
                color: '#BD081C', 
                icon: 'assets/svg_icon/pintrest-icon.svg' 
            },
            { 
                platform: 'youtube', 
                alt: 'YouTube', 
                color: '#FF0000', 
                icon: 'assets/svg_icon/youtube-icon.svg' 
            },
            { 
                platform: 'linkedin', 
                alt: 'LinkedIn', 
                color: '#0A66C2', 
                icon: 'assets/svg_icon/linkdin-icon.svg' 
            },
            { 
                platform: 'twitter', 
                alt: 'X (Twitter)', 
                color: '#000000', 
                icon: 'assets/svg_icon/twitter-icon.svg' 
            },
            { 
                platform: 'facebook', 
                alt: 'Facebook', 
                color: '#1877F2', 
                icon: 'assets/svg_icon/facebook-icon.svg' 
            },
            { 
                platform: 'threads', 
                alt: 'Threads', 
                color: '#000000', 
                icon: 'assets/svg_icon/thread.svg' 
            }
        ];
        
        this.socialIcons = this.generateSocialIcons(15);
        this.centerPosition = Math.floor(this.socialIcons.length / 2);
        
        this.init();
    }
    
    generateSocialIcons(repeatCount) {
        return Array(repeatCount).fill(null).flatMap(() => [...this.baseSocialIcons]);
    }
    
    init() {
        this.handleResize();
        this.createIcons();
        this.startAnimation();
        
        window.addEventListener('resize', () => this.handleResize());
    }
    
    handleResize() {
        const mobile = window.innerWidth <= 640;
        const newSpacing = mobile ? 170 : window.innerWidth <= 768 ? 100 : 130;
        this.spacing = newSpacing;
        this.isMobile = mobile;
        
        if (this.iconsCreated) {
            this.updateIconPositions();
        }
    }
    
    createIcons() {
        const track = document.getElementById('iconsTrack');
        track.innerHTML = '';
        
        this.socialIcons.forEach((social, index) => {
            const iconElement = this.createIconElement(social, index);
            track.appendChild(iconElement);
        });
        
        this.iconsCreated = true;
        this.updateIconPositions();
    }
    
    createIconElement(social, index) {
        const iconDiv = document.createElement('div');
        iconDiv.className = 'social-icon';
        iconDiv.dataset.index = index;
        
        // Create corner brackets
        const brackets = ['tl', 'tr', 'bl', 'br'].map(position => {
            const bracket = document.createElement('span');
            bracket.className = `icon-bracket bracket-${position}`;
            return bracket;
        });
        
        // Create icon container
        const container = document.createElement('div');
        container.className = 'icon-container';
        
        // Create actual image icon
        const icon = document.createElement('img');
        icon.className = 'social-icon-img';
        icon.src = social.icon;
        icon.alt = social.alt;
        icon.loading = 'lazy';
        icon.decoding = 'async';
        
        // Handle image load error - fallback to platform name
        icon.onerror = function() {
            const fallback = document.createElement('div');
            fallback.className = 'social-icon-fallback';
            fallback.textContent = social.platform.charAt(0).toUpperCase();
            fallback.style.cssText = `
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: bold;
                color: ${social.color};
                background: ${social.color}20;
                border-radius: 4px;
                width: 100%;
                height: 100%;
            `;
            this.parentNode.replaceChild(fallback, this);
        };
        
        // Assemble the structure
        brackets.forEach(bracket => iconDiv.appendChild(bracket));
        container.appendChild(icon);
        iconDiv.appendChild(container);
        
        return iconDiv;
    }
    
    getIconPosition(index) {
        const basePosition = (index - this.centerPosition) * this.spacing;
        return basePosition - this.animationOffset;
    }
    
    getIconScale(index) {
        const effectiveCenterIndex = this.centerPosition + this.animationOffset / this.spacing;
        const cycleLength = this.baseSocialIcons.length;
        const iconCyclePosition = index % cycleLength;
        const centerCyclePosition = effectiveCenterIndex % cycleLength;
        
        let distance = Math.abs(iconCyclePosition - centerCyclePosition);
        distance = Math.min(distance, cycleLength - distance);
        
        if (distance < 0.5) return 1;
        if (distance < 1.5) return 0.8;
        if (distance < 2.5) return 0.7;
        return 0.6;
    }
    
    getIconOpacity(index) {
        const effectiveCenterIndex = this.centerPosition + this.animationOffset / this.spacing;
        const cycleLength = this.baseSocialIcons.length;
        const iconCyclePosition = index % cycleLength;
        const centerCyclePosition = effectiveCenterIndex % cycleLength;
        
        let distance = Math.abs(iconCyclePosition - centerCyclePosition);
        distance = Math.min(distance, cycleLength - distance);
        
        if (distance < 0.5) return 1;
        if (distance <= 2.5) return 0.8;
        if (distance <= 3.5) return 0.8;
        return 0.4;
    }
    
    isIconHighlighted(index) {
        const effectiveCenterIndex = this.centerPosition + this.animationOffset / this.spacing;
        const cycleLength = this.baseSocialIcons.length;
        const iconCyclePosition = index % cycleLength;
        const centerCyclePosition = effectiveCenterIndex % cycleLength;
        
        let distance = Math.abs(iconCyclePosition - centerCyclePosition);
        distance = Math.min(distance, cycleLength - distance);
        
        return distance < 0.5;
    }
    
    updateIconPositions() {
        const track = document.getElementById('iconsTrack');
        const icons = track.querySelectorAll('.social-icon');
        
        icons.forEach((iconElement, index) => {
            const social = this.socialIcons[index];
            const scale = this.getIconScale(index);
            const opacity = this.getIconOpacity(index);
            const translateX = this.getIconPosition(index);
            const isHighlighted = this.isIconHighlighted(index);
            
            // Update transform and opacity
            const transform = this.isMobile 
                ? `translateX(${translateX}px)` 
                : `translateX(${translateX}px) scale(${scale})`;
            
            iconElement.style.transform = transform;
            iconElement.style.opacity = opacity;
            iconElement.style.zIndex = isHighlighted ? 20 : 10;
            
            // Update brackets color
            const brackets = iconElement.querySelectorAll('.icon-bracket');
            brackets.forEach(bracket => {
                bracket.style.borderColor = isHighlighted ? social.color : '#d1d5db';
            });
            
            // Update container
            const container = iconElement.querySelector('.icon-container');
            const iconImg = iconElement.querySelector('.social-icon-img, .social-icon-fallback');
            
            if (isHighlighted) {
                container.classList.add('highlighted');
                container.style.backgroundColor = `${social.color}10`;
            } else {
                container.classList.remove('highlighted');
                container.style.backgroundColor = '#f8f9fa';
            }
            
            // Update sizes
            const containerSize = isHighlighted 
                ? (this.isMobile ? '72px' : '96px')
                : (this.isMobile ? '56px' : '62px');
            
            const iconSize = isHighlighted 
                ? (this.isMobile ? '48px' : '64px')
                : (this.isMobile ? '32px' : '40px');
            
            container.style.width = containerSize;
            container.style.height = containerSize;
            container.style.minWidth = containerSize;
            container.style.minHeight = containerSize;
            
            // Update icon size
            if (iconImg.tagName === 'IMG') {
                iconImg.style.width = iconSize;
                iconImg.style.height = iconSize;
                iconImg.style.minWidth = iconSize;
                iconImg.style.minHeight = iconSize;
                iconImg.style.maxWidth = iconSize;
                iconImg.style.maxHeight = iconSize;
            } else {
                // Fallback div
                iconImg.style.fontSize = `${parseInt(iconSize) * 0.6}px`;
            }
        });
    }
    
    startAnimation() {
        this.intervalId = setInterval(() => {
            this.animationOffset += this.spacing;
            
            const cycleLength = this.baseSocialIcons.length * this.spacing;
            
            if (this.animationOffset >= cycleLength) {
                this.roundCount++;
                
                if (this.roundCount >= 15) {
                    this.isRestarting = true;
                    const track = document.getElementById('iconsTrack');
                    track.classList.add('restarting');
                    
                    setTimeout(() => {
                        this.animationOffset = 0;
                        this.roundCount = 0;
                        this.isRestarting = false;
                        track.classList.remove('restarting');
                        this.updateIconPositions();
                    }, 1000);
                    
                    return;
                }
                
                this.animationOffset = 0;
            }
            
            this.updateIconPositions();
        }, 2000);
    }
    
    destroy() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
        }
        window.removeEventListener('resize', this.handleResize);
    }
}

// Initialize the carousel when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new SocialMediaCarousel();
});
