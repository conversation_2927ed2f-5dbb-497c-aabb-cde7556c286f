<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Your Website</title>
    <!-- Bootstrap 5.3 CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css" />
  </head>
  <body>
    <nav class="navbar navbar-expand-lg fixed-top bg-white shadow-sm">
      <div class="container">
        <!-- Logo -->
        <a class="navbar-brand" href="index.html">
          <img
            src="assets/logo.avif"
            alt="Logo"
            class="img-fluid"
            width="128"
          />
        </a>

        <!-- Mobile Toggle Button -->
        <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarNav"
          aria-controls="navbarNav"
          aria-expanded="false"
          aria-label="Toggle navigation"
        >
          <img
            src="assets/hamburgerMenu.jpg"
            alt="Menu"
            style="width: 40px; height: 40px"
          />
        </button>

        <!-- Navigation Links -->

        <div
          class="collapse navbar-collapse justify-content-center"
          id="navbarNav"
        >
          <!-- Sign Up and Login Buttons (Mobile & Tablet Only) -->
          <div class="d-flex d-lg-none flex-column gap-3 w-100">
            <button
              class="btn text-dark fw-medium w-100 text-center"
              onclick="window.location.href='https://app.flowkar.com/sign-up'"
            >
              Sign Up
            </button>
            <button
              class="btn btn-primary w-100 d-flex justify-content-center align-items-center gap-2 px-3 py-2"
              onclick="handleRedirect()"
            >
              <span>Login</span>
              <img
                src="assets/Navbar/leftArrow.svg"
                alt="Left Arrow"
                width="30"
                height="25"
              />
            </button>
          </div>

          <div class="d-flex d-none align-items-center gap-3">
            <button
              class="btn text-dark fw-medium"
              onclick="window.location.href='https://app.flowkar.com/sign-up'"
            >
              Sign Up
            </button>
            <button
              class="btn btn-primary d-flex align-items-center gap-2 px-3 py-2"
              onclick="handleRedirect()"
            >
              <span>Login</span>
              <img
                src="assets/Navbar/leftArrow.svg"
                alt="Left Arrow"
                width="30"
                height="30"
              />
            </button>
          </div>

          <div class="navbar-nav bg-light-gradient rounded-pill px-4 py-2">
            <a class="nav-link active" href="/project-folder/index.html">Home</a>
            <a class="nav-link" href="#solutions-section">Solutions</a>
            <a class="nav-link" href="/project-folder/bloglayout.html">Blogs</a>
            <a class="nav-link" href="/project-folder/about_us.html">About Us</a>
            <a class="nav-link" href="/project-folder/Contactus.html">Contact Us</a>
          </div>
        </div>

        <!-- Sign Up and Login Buttons -->
        <div class="d-none d-lg-flex align-items-center gap-3">
          <button
            class="btn btn-link text-dark fw-medium"
            onclick="window.location.href='https://app.flowkar.com/sign-up'"
          >
            Sign Up
          </button>
          <button
            class="btn btn-primary d-flex align-items-center gap-2 px-3 py-2"
            onclick="handleRedirect()"
          >
            <span>Login</span>
            <img
              src="assets/Navbar/leftArrow.svg"
              alt="Left Arrow"
              width="30"
              height="30"
            />
          </button>
        </div>
      </div>
    </nav>


    <!-- Bootstrap 5.3 JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="js/script.js"></script>
  </body>
</html>
