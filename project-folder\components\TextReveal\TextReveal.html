<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>TextReveal - Pure HTML</title>

    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <link rel="stylesheet" href="/project-folder/css/TextReveal.css" />
  </head>
  <body>
    <section
      class="text-reveal-section position-relative rounded-4 text-white d-flex flex-column flex-md-row align-items-center justify-content-between mx-auto min-vh-100 overflow-hidden"
    >
      <div
        class="header-content position-absolute top-0 start-0 end-0 d-flex flex-column flex-sm-row justify-content-between align-items-start align-items-sm-center z-3"
      >
        <h1 class="text-white fw-medium mb-2 mb-sm-0">Everything</h1>
        <p class="text-orange fw-light mb-0">a team needs</p>
      </div>

      <div
        class="content-section w-100 position-relative z-3 d-flex flex-column align-items-center align-items-md-start"
      >
        <div class="text-reveal-container" id="textRevealContainer">
          <div class="reveal-lines-wrapper text-center text-sm-start">
            <div class="text-reveal-line" data-line="0">
              <span class="gradient-text-primary">One platform</span>
            </div>
            <div class="text-reveal-line" data-line="1">
              <span class="gradient-text-primary">Every story No chaos</span>
            </div>
            <div class="text-reveal-line" data-line="2">
              <span class="text-white">They juggled tools,</span>
            </div>
            <div class="text-reveal-line" data-line="3">
              <span class="text-white">Tabs, and time</span>
            </div>
            <div class="text-reveal-line" data-line="4">
              <span class="text-white">Just to post one story</span>
            </div>
            <div class="text-reveal-line" data-line="5">
              <span class="gradient-text-blue-purple"
                >Flowkar changed the game</span
              >
            </div>
            <div class="text-reveal-line" data-line="6">
              <span class="text-white">Syncing every platform into one</span>
            </div>
            <div class="text-reveal-line" data-line="7">
              <span class="text-white">Now, creators create</span>
            </div>
            <div class="text-reveal-line" data-line="8">
              <span class="gradient-text-green-blue">And Flowkar</span>
            </div>
            <div class="text-reveal-line" data-line="9">
              <span class="gradient-text-primary">takes care of</span>
            </div>
            <div class="text-reveal-line" data-line="10">
              <span class="gradient-text-primary">the rest.</span>
            </div>
          </div>

          <div
            class="app-store-badges d-flex flex-column flex-sm-row gap-3 mt-4 mt-sm-5 align-items-center align-items-md-start justify-content-center justify-content-md-start"
          >
            <div
              class="store-button app-store-btn d-flex align-items-center justify-content-between"
              onclick="window.open('https://apps.apple.com/in/app/flowkar-social-powerhouse/id6740058663', '_blank')"
            >
              <div class="text-start flex-grow-1">
                <div class="text-black store-text">App Store</div>
              </div>
              <div
                class="store-icon-container d-flex align-items-center justify-content-center ms-2 ms-sm-4 flex-shrink-0"
              >
                <svg
                  class="store-icon text-black"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"
                  />
                </svg>
              </div>
            </div>

            <div
              class="store-button play-store-btn d-flex align-items-center justify-content-between"
              onclick="window.open('https://play.google.com/store/apps/details?id=com.app.flowkar&hl=en', '_blank')"
            >
              <div class="text-start flex-grow-1">
                <div class="text-black store-text">Google Play</div>
              </div>
              <div
                class="store-icon-container d-flex align-items-center justify-content-center ms-2 ms-sm-4 flex-shrink-0"
              >
                <svg
                  class="store-icon text-black"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    d="M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.61 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.9 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z"
                  />
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div
        class="phone-mockup-section w-100 mt-4 mt-md-0 d-flex justify-content-center align-items-center position-relative z-3"
      >
        <div class="phone-mockup position-relative mx-auto">
          <img
            src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Iphone-xsOvOBR1J8C3OQJgbba4viZOMtBgUw.svg"
            alt="phone"
            class="w-100 h-auto"
          />
        </div>
      </div>

      <div
        class="bottom-gradient position-absolute bottom-0 start-0 end-0"
      ></div>
    </section>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <script src="/project-folder/js/TextReveal.js"></script>
  </body>
</html>
