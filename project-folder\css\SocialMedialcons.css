/* ------------------- SocialMediaIcons ------------------------ */

/* Custom styles for exact match with React version */

/* Typography matching */
.display-4 {
    font-size: 2.6875rem;
    line-height: 1.2;
}

@media (min-width: 768px) {
    .display-4 {
        font-size: 2.375rem;
    }
}

@media (min-width: 992px) {
    .display-4 {
        font-size: 2.6875rem;
    }
}

@media (min-width: 1200px) {
    .display-4 {
        font-size: 2.6875rem;
    }
}

.lead {
    font-size: 1rem;
}

@media (min-width: 768px) {
    .lead {
        font-size: 1.25rem;
    }
}

@media (min-width: 992px) {
    .lead {
        font-size: 1.5rem;
    }
}

/* Icon container styles */
.social-icon-item {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 9px;
    transition: all 1s ease-in-out;
}

.social-icon-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 9px;
    transition: all 1s ease-in-out;
    flex-shrink: 0;
}

.social-icon-img {
    object-fit: contain;
    position: relative;
    z-index: 10;
    transition: all 1s ease;
    flex-shrink: 0;
}

/* Corner brackets */
.corner-bracket {
    position: absolute;
    width: 1.5rem;
    height: 1.5rem;
    border-width: 2px;
    border-style: solid;
    z-index: 20;
    transition: border-color 1s ease;
}

.corner-top-left {
    top: -0.75rem;
    left: -0.75rem;
    border-top: 2px solid;
    border-left: 2px solid;
    border-radius: 10px 0 0 0;
}

.corner-top-right {
    top: -0.75rem;
    right: -0.75rem;
    border-top: 2px solid;
    border-right: 2px solid;
    border-radius: 0 10px 0 0;
}

.corner-bottom-left {
    bottom: -0.75rem;
    left: -0.75rem;
    border-bottom: 2px solid;
    border-left: 2px solid;
    border-radius: 0 0 0 10px;
}

.corner-bottom-right {
    bottom: -0.75rem;
    right: -0.75rem;
    border-bottom: 2px solid;
    border-right: 2px solid;
    border-radius: 0 0 10px 0;
}

/* Mobile adjustments */
@media (max-width: 640px) {
    #iconsContainer {
        height: 120px !important;
    }
    
    .corner-top-left,
    .corner-bottom-left {
        left: -1.5rem;
    }
    
    .corner-top-right,
    .corner-bottom-right {
        right: -1.5rem;
    }
}

/* Transition utilities */
.transition-opacity {
    transition: opacity 1s ease;
}

/* Background positioning */
.bg-shadow {
    background-image: url('Background_Shadow.svg');
    background-repeat: no-repeat;
    background-position: center;
    background-size: 583px 500px;
}

.bg-pattern {
    background-image: url('Background_03.svg');
    background-repeat: no-repeat;
    background-position: center;
    background-size: 583px 500px;
}

/* Ensure proper font weights */
.fw-medium {
    font-weight: 500 !important;
}

.fw-light {
    font-weight: 300 !important;
}

/* Color overrides */
.text-dark-custom {
    color: #563D39 !important;
}

.text-muted-custom {
    color: #00000099 !important;
}