<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Testimonials - What Our Clients Said</title>
    
    <!-- Bootstrap 5.3.x CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/project-folder/css/style.css">
</head>
<body>
    

    <!-- Testimonials Section -->
    <section class="testimonials-section">
        <div class="container-fluid px-4 py-5">
            <div class="text-center">
                <!-- Header -->
                <h1 class="testimonials-title mb-4 mb-md-5">
                    What Our Client Said
                </h1>

                <!-- Testimonials Container -->
                <div class="testimonials-container position-relative mx-auto p-2 p-md-3">
                    <!-- Bootstrap Carousel -->
                    <div id="testimonialsCarousel" class="carousel slide" data-bs-ride="false">
                        <div class="carousel-inner">
                            <!-- Slide 1 -->
                            <div class="carousel-item active">
                                <div class="row justify-content-center g-3 g-md-4">
                                    <div class="col-12 col-md-6 col-lg-4">
                                        <div class="testimonial-card">
                                            <div class="profile-section d-flex align-items-center mb-3 mb-md-4">
                                                <div class="profile-image me-3">
                                                    <img src="/project-folder/assets/Testimonials/Girl_01.png" alt="Riya Patel" class="img-fluid">
                                                </div>
                                                <div class="profile-info text-start">
                                                    <h3 class="profile-name">Riya Patel</h3>
                                                    <p class="profile-role">Content Strategist</p>
                                                </div>
                                            </div>
                                            <p class="testimonial-content text-start">
                                                Flowkar completely changed how I manage my brand's social media. The UI is so intuitive, I scheduled a month's worth of content in just one evening!
                                            </p>
                                        </div>
                                    </div>
                                    <div class="col-12 col-md-6 col-lg-4">
                                        <div class="testimonial-card">
                                            <div class="profile-section d-flex align-items-center mb-3 mb-md-4">
                                                <div class="profile-image me-3">
                                                    <img src="/project-folder/assets/Testimonials/boy-01.png" alt="Siddharth Mehta" class="img-fluid">
                                                </div>
                                                <div class="profile-info text-start">
                                                    <h3 class="profile-name">Siddharth Mehta</h3>
                                                    <p class="profile-role">Agency Founder</p>
                                                </div>
                                            </div>
                                            <p class="testimonial-content text-start">
                                                We used to juggle 4 different tools just to stay active online. With Flowkar, our entire team collaborates, schedules, and tracks performance all in one place.
                                            </p>
                                        </div>
                                    </div>
                                    <div class="col-12 col-md-6 col-lg-4">
                                        <div class="testimonial-card">
                                            <div class="profile-section d-flex align-items-center mb-3 mb-md-4">
                                                <div class="profile-image me-3">
                                                    <img src="/project-folder/assets/Testimonials/girl-02.png" alt="Tanya Rawal" class="img-fluid">
                                                </div>
                                                <div class="profile-info text-start">
                                                    <h3 class="profile-name">Tanya Rawal</h3>
                                                    <p class="profile-role">Digital Creator</p>
                                                </div>
                                            </div>
                                            <p class="testimonial-content text-start">
                                                The ability to post across Instagram, LinkedIn, Facebook, and even YouTube Shorts with one click is a game-changer. Total time-saver!
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Slide 2 -->
                            <div class="carousel-item">
                                <div class="row justify-content-center g-3 g-md-4">
                                    <div class="col-12 col-md-6 col-lg-4">
                                        <div class="testimonial-card">
                                            <div class="profile-section d-flex align-items-center mb-3 mb-md-4">
                                                <div class="profile-image me-3">
                                                    <img src="/project-folder/assets/Testimonials/boy-02.png" alt="Amit Bhansali" class="img-fluid">
                                                </div>
                                                <div class="profile-info text-start">
                                                    <h3 class="profile-name">Amit Bhansali</h3>
                                                    <p class="profile-role">Social Media Intern</p>
                                                </div>
                                            </div>
                                            <p class="testimonial-content text-start">
                                                I love how Flowkar simplifies everything. Drafting content, adding hashtags, and scheduling it with smart recommendations is just... effortless.
                                            </p>
                                        </div>
                                    </div>
                                    <div class="col-12 col-md-6 col-lg-4">
                                        <div class="testimonial-card">
                                            <div class="profile-section d-flex align-items-center mb-3 mb-md-4">
                                                <div class="profile-image me-3">
                                                    <img src="/project-folder/assets/Testimonials/girl-03.png" alt="Neha Joshi" class="img-fluid">
                                                </div>
                                                <div class="profile-info text-start">
                                                    <h3 class="profile-name">Neha Joshi</h3>
                                                    <p class="profile-role">Product Marketer</p>
                                                </div>
                                            </div>
                                            <p class="testimonial-content text-start">
                                                Finally, a platform that's not only powerful but looks good too. The interface is smooth, modern, and super fast.
                                            </p>
                                        </div>
                                    </div>
                                    <div class="col-12 col-md-6 col-lg-4">
                                        <div class="testimonial-card">
                                            <div class="profile-section d-flex align-items-center mb-3 mb-md-4">
                                                <div class="profile-image me-3">
                                                    <img src="/project-folder/assets/Testimonials/boy-03.png" alt="Arjun Desai" class="img-fluid">
                                                </div>
                                                <div class="profile-info text-start">
                                                    <h3 class="profile-name">Arjun Desai</h3>
                                                    <p class="profile-role">Fitness Influencer</p>
                                                </div>
                                            </div>
                                            <p class="testimonial-content text-start">
                                                Flowkar's analytics helped me identify the best time to post for engagement. I saw a 60% boost in reach within a week!
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Slide 3 -->
                            <div class="carousel-item">
                                <div class="row justify-content-center g-3 g-md-4">
                                    <div class="col-12 col-md-6 col-lg-4">
                                        <div class="testimonial-card">
                                            <div class="profile-section d-flex align-items-center mb-3 mb-md-4">
                                                <div class="profile-image me-3">
                                                    <img src="/project-folder/assets/Testimonials/boy-04.png" alt="Rahul Nair" class="img-fluid">
                                                </div>
                                                <div class="profile-info text-start">
                                                    <h3 class="profile-name">Rahul Nair</h3>
                                                    <p class="profile-role">Startup Founder</p>
                                                </div>
                                            </div>
                                            <p class="testimonial-content text-start">
                                                As a startup founder, I needed speed and clarity. Flowkar gave me both — powerful features without the learning curve.
                                            </p>
                                        </div>
                                    </div>
                                    <div class="col-12 col-md-6 col-lg-4">
                                        <div class="testimonial-card">
                                            <div class="profile-section d-flex align-items-center mb-3 mb-md-4">
                                                <div class="profile-image me-3">
                                                    <img src="/project-folder/assets/Testimonials/girl-04.png" alt="Mitali Kapoor" class="img-fluid">
                                                </div>
                                                <div class="profile-info text-start">
                                                    <h3 class="profile-name">Mitali Kapoor</h3>
                                                    <p class="profile-role">Campaign Manager</p>
                                                </div>
                                            </div>
                                            <p class="testimonial-content text-start">
                                                Flowkar made launching our campaign across multiple platforms feel like a breeze. Seamless, smart, and stress-free!
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Navigation Controls -->
                        <div class="carousel-controls d-flex justify-content-center align-items-center mt-4 mt-md-5">
                            <button class="carousel-control-btn me-3 me-md-4" type="button" data-bs-target="#testimonialsCarousel" data-bs-slide="prev">
                                <svg class="carousel-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                </svg>
                            </button>
                            
                            <button class="carousel-control-btn carousel-control-next-btn" type="button" data-bs-target="#testimonialsCarousel" data-bs-slide="next">
                                <svg class="carousel-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Bootstrap 5.3.x JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="/project-folder/js/Testimonials.js"></script>
</body>
</html>