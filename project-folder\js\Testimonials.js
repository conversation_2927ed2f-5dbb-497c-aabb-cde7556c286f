// ===== TESTIMONIALS FUNCTIONALITY =====
document.addEventListener('DOMContentLoaded', function() {
    // Initialize carousel
    const carousel = document.getElementById('testimonialsCarousel');
    const bsCarousel = new bootstrap.Carousel(carousel, {
        interval: false, // Disable auto-play
        wrap: true,
        touch: true
    });

    // Dark mode functionality
    const darkModeToggle = document.getElementById('darkModeToggle');
    const html = document.documentElement;
    const darkModeIcon = darkModeToggle.querySelector('.dark-mode-icon');

    // Check for saved theme preference or default to light mode
    const savedTheme = localStorage.getItem('theme') || 'light';
    html.setAttribute('data-bs-theme', savedTheme);
    updateDarkModeIcon(savedTheme);

    // Dark mode toggle event listener
    darkModeToggle.addEventListener('click', function() {
        const currentTheme = html.getAttribute('data-bs-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        
        html.setAttribute('data-bs-theme', newTheme);
        localStorage.setItem('theme', newTheme);
        updateDarkModeIcon(newTheme);
        
        // Add a subtle animation to the toggle button
        darkModeToggle.style.transform = 'scale(0.9)';
        setTimeout(() => {
            darkModeToggle.style.transform = 'scale(1)';
        }, 150);
    });

    function updateDarkModeIcon(theme) {
        darkModeIcon.textContent = theme === 'dark' ? '☀️' : '🌙';
    }

    // Enhanced touch/swipe functionality for mobile
    let touchStartX = 0;
    let touchEndX = 0;
    let touchStartY = 0;
    let touchEndY = 0;
    let isSwiping = false;

    const carouselInner = carousel.querySelector('.carousel-inner');

    // Touch event listeners
    carouselInner.addEventListener('touchstart', handleTouchStart, { passive: true });
    carouselInner.addEventListener('touchmove', handleTouchMove, { passive: false });
    carouselInner.addEventListener('touchend', handleTouchEnd, { passive: true });

    function handleTouchStart(e) {
        touchStartX = e.touches[0].clientX;
        touchStartY = e.touches[0].clientY;
        isSwiping = false;
    }

    function handleTouchMove(e) {
        if (!touchStartX || !touchStartY) return;

        touchEndX = e.touches[0].clientX;
        touchEndY = e.touches[0].clientY;

        const deltaX = Math.abs(touchStartX - touchEndX);
        const deltaY = Math.abs(touchStartY - touchEndY);

        // Determine if this is a horizontal swipe
        if (deltaX > deltaY && deltaX > 10) {
            isSwiping = true;
            e.preventDefault(); // Prevent scrolling
        }
    }

    function handleTouchEnd(e) {
        if (!touchStartX || !touchEndX || !isSwiping) return;

        const deltaX = touchStartX - touchEndX;
        const minSwipeDistance = 50;

        if (Math.abs(deltaX) > minSwipeDistance) {
            if (deltaX > 0) {
                // Swipe left - next slide
                bsCarousel.next();
            } else {
                // Swipe right - previous slide
                bsCarousel.prev();
            }
        }

        // Reset values
        touchStartX = 0;
        touchEndX = 0;
        touchStartY = 0;
        touchEndY = 0;
        isSwiping = false;
    }

    // Keyboard navigation
    carousel.addEventListener('keydown', function(e) {
        if (e.key === 'ArrowLeft') {
            e.preventDefault();
            bsCarousel.prev();
        } else if (e.key === 'ArrowRight') {
            e.preventDefault();
            bsCarousel.next();
        }
    });

    // Add focus management for accessibility
    const carouselControls = carousel.querySelectorAll('.carousel-control-btn');
    carouselControls.forEach(control => {
        control.addEventListener('focus', function() {
            this.style.outline = '2px solid var(--primary-color)';
            this.style.outlineOffset = '2px';
        });

        control.addEventListener('blur', function() {
            this.style.outline = 'none';
        });
    });

    // Enhanced card interactions
    const testimonialCards = document.querySelectorAll('.testimonial-card');
    
    testimonialCards.forEach(card => {
        // Add ripple effect on click for touch devices
        card.addEventListener('click', function(e) {
            if (window.innerWidth <= 768) {
                const ripple = document.createElement('div');
                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;
                
                ripple.style.cssText = `
                    position: absolute;
                    width: ${size}px;
                    height: ${size}px;
                    left: ${x}px;
                    top: ${y}px;
                    background: rgba(86, 61, 57, 0.3);
                    border-radius: 50%;
                    transform: scale(0);
                    animation: ripple 0.6s ease-out;
                    pointer-events: none;
                    z-index: 1;
                `;
                
                this.style.position = 'relative';
                this.style.overflow = 'hidden';
                this.appendChild(ripple);
                
                setTimeout(() => {
                    ripple.remove();
                }, 600);
            }
        });

        // Add hover sound effect (optional)
        card.addEventListener('mouseenter', function() {
            if (window.innerWidth > 768) {
                // You can add a subtle sound effect here if desired
                // new Audio('hover-sound.mp3').play().catch(() => {});
            }
        });
    });

    // Add CSS animation for ripple effect
    const style = document.createElement('style');
    style.textContent = `
        @keyframes ripple {
            to {
                transform: scale(2);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(style);

    // Performance optimization: Intersection Observer for lazy loading
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src || img.src;
                    img.classList.remove('lazy');
                    observer.unobserve(img);
                }
            });
        });

        const lazyImages = document.querySelectorAll('img[data-src]');
        lazyImages.forEach(img => imageObserver.observe(img));
    }

    // Auto-adjust carousel height for better mobile experience
    function adjustCarouselHeight() {
        const activeSlide = carousel.querySelector('.carousel-item.active');
        if (activeSlide) {
            const maxHeight = Math.max(...Array.from(activeSlide.querySelectorAll('.testimonial-card')).map(card => card.offsetHeight));
            activeSlide.style.minHeight = `${maxHeight}px`;
        }
    }

    // Adjust height on slide change
    carousel.addEventListener('slid.bs.carousel', adjustCarouselHeight);
    
    // Adjust height on window resize
    window.addEventListener('resize', debounce(adjustCarouselHeight, 250));

    // Initial height adjustment
    setTimeout(adjustCarouselHeight, 100);

    // Utility function for debouncing
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Add smooth scrolling behavior for better UX
    document.documentElement.style.scrollBehavior = 'smooth';

    // Preload next/previous slide images for better performance
    carousel.addEventListener('slide.bs.carousel', function(e) {
        const nextSlide = e.relatedTarget;
        const images = nextSlide.querySelectorAll('img');
        images.forEach(img => {
            if (!img.complete) {
                const tempImg = new Image();
                tempImg.src = img.src;
            }
        });
    });

    // Add loading state management
    window.addEventListener('load', function() {
        document.body.classList.add('loaded');
        
        // Add a subtle fade-in animation
        const testimonialSection = document.querySelector('.testimonials-section');
        testimonialSection.style.opacity = '0';
        testimonialSection.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            testimonialSection.style.transition = 'opacity 0.6s ease-out, transform 0.6s ease-out';
            testimonialSection.style.opacity = '1';
            testimonialSection.style.transform = 'translateY(0)';
        }, 100);
    });

    // Error handling for images
    const images = document.querySelectorAll('.profile-image img');
    images.forEach(img => {
        img.addEventListener('error', function() {
            this.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNGM0Y0RjYiLz4KPHBhdGggZD0iTTIwIDEwQzE2LjY4NjMgMTAgMTQgMTIuNjg2MyAxNCAxNkMxNCAxOS4zMTM3IDE2LjY4NjMgMjIgMjAgMjJDMjMuMzEzNyAyMiAyNiAxOS4zMTM3IDI2IDE2QzI2IDEyLjY4NjMgMjMuMzEzNyAxMCAyMCAxMFoiIGZpbGw9IiM5Q0EzQUYiLz4KPHBhdGggZD0iTTEwIDMwQzEwIDI0LjQ3NzIgMTQuNDc3MiAyMCAyMCAyMEMyNS41MjI4IDIwIDMwIDI0LjQ3NzIgMzAgMzBWMzJIMTBWMzBaIiBmaWxsPSIjOUNBM0FGIi8+Cjwvc3ZnPgo=';
            this.alt = 'Profile placeholder';
        });
    });

    console.log('Testimonials component initialized successfully!');
});