
/*---------------------- Thoughts ------------------------- */

/* Main Container */
.thoughts-container {
    width: 90%;
    margin: 0 auto;
    background-color: #563D39;
    padding: 60px 1rem 40px;
    border-radius: 40px;
}

.thoughts-inner {
    width: 95%;
    margin: 0 auto;
}

/* Header Styles */
.thoughts-title {
    color: white;
    font-size: 24px;
    font-weight: 600;
    margin: 0;
}

.thoughts-subtitle {
    color: #AA8882;
    font-weight: 600;
    font-size: 18px;
    margin: 0;
}

/* Responsive Header */
@media (min-width: 576px) {
    .thoughts-title {
        font-size: 28px;
    }
    .thoughts-subtitle {
        font-size: 22px;
    }
}

@media (min-width: 768px) {
    .thoughts-title {
        font-size: 36px;
    }
    .thoughts-subtitle {
        font-size: 28px;
    }
}

@media (min-width: 992px) {
    .thoughts-title {
        font-size: 40px;
    }
    .thoughts-subtitle {
        font-size: 34px;
    }
}

/* Grid Layout */
.thoughts-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
    height: auto;
}

@media (min-width: 768px) {
    .thoughts-grid {
        grid-template-columns: repeat(6, 1fr);
        grid-template-rows: repeat(4, 1fr);
        height: 800px;
    }
    
    .large-card {
        grid-column: span 2;
        grid-row: span 3;
    }
    
    .landscape-card {
        grid-column: span 4;
        grid-row: span 2;
    }
    
    .small-card-1,
    .small-card-2,
    .bottom-left-card,
    .bottom-card-1,
    .bottom-card-2 {
        grid-column: span 2;
        grid-row: span 1;
    }
}

/* Mobile Grid Heights */
.grid-item {
    height: 200px;
}

.large-card {
    height: 500px;
}

.landscape-card {
    height: 350px;
}

@media (min-width: 768px) {
    .grid-item {
        height: auto;
    }
}

/* Blog Card Styles */
.blog-card {
    position: relative;
    border: 1px solid #FFFFFF;
    border-radius: 20px;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.blog-card:hover {
    transform: translateY(-2px);
}

.card-image-container {
    position: relative;
    border-radius: 20px 20px 0 0;
    overflow: hidden;
}

.card-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 20px;
    transition: transform 0.5s ease;
}

.blog-card:hover .card-image {
    transform: scale(1.05);
}

.card-overlay {
    position: absolute;
    inset: 0;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.5), transparent, transparent);
    border-radius: 20px;
}

/* Large Card Content */
.card-content-large {
    padding: 1rem;
    color: white;
    background-color: #563D39;
    border-radius: 0 0 20px 20px;
}

.card-title-large {
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
    line-height: 1.3;
}

.card-subtitle {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 0.25rem;
}

.card-description {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.5;
    margin: 0;
}

@media (min-width: 576px) {
    .card-title-large {
        font-size: 1.125rem;
    }
    .card-subtitle,
    .card-description {
        font-size: 0.875rem;
    }
}

@media (min-width: 768px) {
    .card-content-large {
        padding: 1.5rem;
    }
    .card-title-large {
        font-size: 1.25rem;
    }
}

/* Regular Card Content */
.card-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 0.75rem;
    color: white;
}

@media (min-width: 768px) {
    .card-content {
        padding: 1rem;
    }
}

.card-title {
    font-size: 0.875rem;
    font-weight: bold;
    margin: 0;
}

.card-title-small {
    font-size: 0.75rem;
    font-weight: bold;
    margin: 0;
}

@media (min-width: 576px) {
    .card-title {
        font-size: 1rem;
    }
    .card-title-small {
        font-size: 0.875rem;
    }
}

@media (min-width: 768px) {
    .card-title {
        font-size: 1.125rem;
    }
    .card-title-small {
        font-size: 1rem;
    }
}

/* Play Button */
.play-button {
    position: absolute;
    inset: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.play-button-outer {
    height: 92px;
    width: 92px;
    border: 1px solid rgba(255, 255, 255, 0.4);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.3s ease;
}

.play-button-inner {
    height: 76px;
    width: 76px;
    border-radius: 50%;
    padding: 8px;
    background-color: rgba(255, 255, 255, 0.24);
    display: flex;
    align-items: center;
    justify-content: center;
}

.play-icon {
    width: 40px;
    height: 40px;
    color: white;
}

.blog-card:hover .play-button-outer {
    transform: scale(1.1);
}

/* View All Button */
.view-all-btn {
    background-color: white;
    color: black;
    border: none;
    border-radius: 6px;
    width: 150px;
    height: 42px;
    padding: 6px 6px 6px 20px;
    margin-top: 20px;
    font-weight: normal;
    transition: all 0.3s ease;
}

.view-all-btn:hover {
    background-color: #f8f9fa;
    color: black;
    transform: translateY(-1px);
}

.arrow-icon {
    width: 30px;
    height: 30px;
}

/* Skeleton Loading Styles */
.skeleton-card {
    position: relative;
    background-color: #6d4c46;
    border: 1px solid #FFFFFF;
    border-radius: 20px;
    overflow: hidden;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.skeleton-gradient {
    position: absolute;
    inset: 0;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.3), transparent, transparent);
    border-radius: 20px;
}

.skeleton-content {
    padding: 1rem;
}

.skeleton-line {
    height: 1rem;
    border-radius: 0.25rem;
    margin-bottom: 0.5rem;
}

.skeleton-line:last-child {
    margin-bottom: 0;
}

.skeleton-line-lg {
    background-color: rgba(255, 255, 255, 0.3);
    width: 75%;
}

.skeleton-line-md {
    background-color: rgba(255, 255, 255, 0.2);
    width: 50%;
    height: 0.75rem;
}

.skeleton-line-sm {
    background-color: rgba(255, 255, 255, 0.1);
    width: 100%;
    height: 0.75rem;
}

.skeleton-play-button {
    position: absolute;
    inset: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.skeleton-play-outer {
    height: 92px;
    width: 92px;
    border: 1px solid rgba(255, 255, 255, 0.4);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.1);
}

.skeleton-play-inner {
    height: 76px;
    width: 76px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* Utility Classes */
.d-none {
    display: none !important;
}