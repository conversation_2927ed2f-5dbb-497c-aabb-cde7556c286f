// JavaScript for TextReveal scroll animation and interactions

class TextRevealAnimation {
    constructor() {
        this.container = document.getElementById('textRevealContainer');
        this.lines = document.querySelectorAll('.text-reveal-line');
        this.activeLines = new Set();
        this.animationCompleted = false;
        
        this.init();
    }
    
    init() {
        this.handleScroll = this.handleScroll.bind(this);
        window.addEventListener('scroll', this.handleScroll);
        this.handleScroll(); // Initial check
    }
    
    handleScroll() {
        // If animation is already completed, don't do anything
        if (this.animationCompleted) return;
        
        if (!this.container) return;
        
        const rect = this.container.getBoundingClientRect();
        const windowHeight = window.innerHeight;
        
        // Improved scroll calculation - responsive trigger points based on screen size
        const elementTop = rect.top;
        const isMobile = windowHeight < 768; // Mobile breakpoint
        const triggerPoint = isMobile ? windowHeight * 0.6 : windowHeight * 0.5;
        const endPoint = isMobile ? windowHeight * 0.2 : windowHeight * 0.1;
        
        let scrollProgress = 0;
        if (elementTop < triggerPoint) {
            const scrollDistance = triggerPoint - endPoint;
            const currentScroll = triggerPoint - elementTop;
            scrollProgress = Math.max(0, Math.min(1, currentScroll / scrollDistance));
        }
        
        // Calculate how many lines should be active based on scroll progress
        const activeCount = Math.floor(scrollProgress * this.lines.length);
        const newActiveLines = new Set(
            Array.from({ length: activeCount }, (_, i) => i)
        );
        
        // Update active lines
        this.lines.forEach((line, index) => {
            if (newActiveLines.has(index)) {
                line.classList.add('active');
            } else {
                line.classList.remove('active');
            }
        });
        
        this.activeLines = newActiveLines;
        
        // Check if all lines are now active (animation completed)
        if (activeCount >= this.lines.length) {
            this.animationCompleted = true;
            // Remove scroll listener since animation is complete
            window.removeEventListener('scroll', this.handleScroll);
        }
    }
    
    destroy() {
        window.removeEventListener('scroll', this.handleScroll);
    }
}

// App Store redirect functions
function handleRedirectAppStore() {
    window.location.href = "https://apps.apple.com/in/app/flowkar/id6740058663";
}

function handleRedirectPlayStore() {
    window.location.href = "https://play.google.com/store/apps/details?id=com.app.flowkar&hl=en";
}

// Initialize the animation when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    new TextRevealAnimation();
});

// Handle page visibility changes to optimize performance
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        // Page is hidden, could pause animations if needed
    } else {
        // Page is visible, resume animations if needed
    }
});

// Debounce scroll events for better performance
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Optional: Add smooth scrolling behavior
document.documentElement.style.scrollBehavior = 'smooth';