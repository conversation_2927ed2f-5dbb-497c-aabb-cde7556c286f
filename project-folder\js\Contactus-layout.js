 fetch('components/header/header.html')
  .then(res => res.text())
  .then(data => {
    document.getElementById('header').innerHTML = data;
  });



  fetch('components/Contactus/ContactusHero.html')
  .then(res => res.text())
  .then(data => {
    document.getElementById('ContactusHero').innerHTML = data;
  });




  fetch('components/Contactus/Conversation.html')
  .then(res => res.text())
  .then(data => {
    document.getElementById('Conversation').innerHTML = data;
  });


  fetch('components/Contactus/Features.html')
  .then(res => res.text())
  .then(data => {
    document.getElementById('Features').innerHTML = data;
  });


 
fetch('components/footer/footer.html')
  .then(res => res.text())
  .then(data => {
    document.getElementById('footer').innerHTML = data;
  });

  