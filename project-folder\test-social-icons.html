<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Social Icons Component</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
        }
        
        .test-container {
            background-color: #f8f9fa;
            padding: 20px;
            margin: 20px;
            border-radius: 8px;
        }
        
        .test-info {
            background-color: #e3f2fd;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
            border-left: 4px solid #2196f3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-info">
            <h2>Social Icons Component Test</h2>
            <p>This page tests the social icons component when loaded dynamically (similar to how it's loaded in the main application).</p>
            <p><strong>Expected behavior:</strong> Social media icons should appear with animations and proper styling.</p>
        </div>
        
        <!-- This div will contain the loaded component -->
        <div id="socialIcons"></div>
        
        <div class="test-info" style="margin-top: 20px;">
            <h3>Debug Information</h3>
            <p id="loadStatus">Loading component...</p>
            <p id="scriptStatus">Script status: Not executed</p>
            <p id="elementStatus">Elements status: Not checked</p>
        </div>
    </div>

    <script>
        // Debug function to check component status
        function checkComponentStatus() {
            const container = document.getElementById('socialIcons');
            const iconsTrack = document.getElementById('iconsTrack');
            const socialIcons = container.querySelectorAll('.social-icon');
            
            document.getElementById('elementStatus').textContent = 
                `Elements status: Container ${container ? 'found' : 'not found'}, ` +
                `Track ${iconsTrack ? 'found' : 'not found'}, ` +
                `Icons count: ${socialIcons.length}`;
        }

        // Load the social icons component
        fetch('components/SocialMediaIcons/socialIcons.html')
            .then(res => res.text())
            .then(data => {
                document.getElementById('loadStatus').textContent = 'Component HTML loaded successfully';
                document.getElementById('socialIcons').innerHTML = data;
                
                // Execute any scripts in the loaded component
                const scripts = document.getElementById('socialIcons').querySelectorAll('script');
                scripts.forEach(script => {
                    const newScript = document.createElement('script');
                    newScript.textContent = script.textContent;
                    document.head.appendChild(newScript);
                });
                
                document.getElementById('scriptStatus').textContent = `Script status: ${scripts.length} script(s) executed`;
                
                // Check status after a short delay
                setTimeout(checkComponentStatus, 500);
                setTimeout(checkComponentStatus, 2000);
            })
            .catch(error => {
                document.getElementById('loadStatus').textContent = 'Error loading component: ' + error.message;
                console.error('Error loading social icons component:', error);
            });
    </script>
</body>
</html>
