<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hero Section</title>

    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Figtree:wght@300;400;600&display=swap" rel="stylesheet">

    <link rel="stylesheet" href="/project-folder/css/Contact_Us.css">
</head>
<body>

    


    <div id="hero-main" class="hero-main ">
        <div class="container-fluid px-3 px-sm-4">
            <header class="hero-header">
                <div class="row align-items-center h-100">

                    <div class="col-12 col-md-6 d-flex flex-column align-items-center align-items-md-start py-4">
                        <div class="hero-title">
                            <p class="mb-0">
                                Talk To Flowkar <br> We're <br>
                            </p>
                            <span class="position-relative">
                                <p class="mb-0">All Ears</p>
                                <svg class="circle-decoration" width="270" height="95" viewBox="0 0 270 95" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M110.548 2.81196C33.1064 -5.40085 -41.1588 50.765 31.9206 77.6346C115.102 108.218 263.261 89.2276 267.88 54.9691C272.852 18.0976 121.98 -6.74839 48.6587 24.9825" stroke="white" stroke-width="3.79883"/>
                                </svg>
                            </span>
                        </div>
                        <p class="hero-description text-center text-md-start">
                            Whether it's a query, feedback, or just a "Hi" - we're here and ready to respond fast.
                        </p>
                    </div>


                    <div class="col-12 col-md-6 d-flex justify-content-center justify-content-md-end align-items-center align-items-md-end">
                        <div class="hero-image">
                            <svg width="260" height="320" viewBox="0 0 260 320" fill="none" xmlns="http://www.w3.org/2000/svg">
  
                                <rect width="260" height="320" rx="40" fill="rgba(255,255,255,0.1)"/>
                                <text x="130" y="160" text-anchor="middle" fill="white" font-size="16">Dummy Image</text>
                            </svg>
                        </div>
                    </div>
                </div>
            </header>
        </div>
    </div>


    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <script src="/project-folder/js/Contact_Us.js"></script>
</body>
</html>
