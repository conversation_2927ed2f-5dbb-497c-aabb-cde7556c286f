 fetch('components/header/header.html')
  .then(res => res.text())
  .then(data => {
    document.getElementById('header').innerHTML = data;
  });



 fetch('components/Privacy_Policy/Privacy_Policy.html')
  .then(res => res.text())
  .then(data => {
    document.getElementById('Privacy_Policy').innerHTML = data;
  });

 
fetch('components/footer/footer.html')
  .then(res => res.text())
  .then(data => {
    document.getElementById('footer').innerHTML = data;
  });

 
