
/* ---------------------------- TextReveal ---------------------------- */




/* Custom CSS for TextReveal Component */

/* Root variables for consistent theming */
:root {
    --primary-bg: #563D39;
    --text-orange: #FED7AA;
    --gradient-primary: linear-gradient(to right, #ffffff, #d1d5db);
    --gradient-blue-purple: linear-gradient(to right, #60a5fa, #a78bfa);
    --gradient-green-blue: linear-gradient(to right, #4ade80, #60a5fa);
    --store-bg: #ffffff;
    --store-icon-bg: rgba(86, 61, 57, 0.2);
}

/* Main section styling */
.text-reveal-section {
    background-color: var(--primary-bg);
    padding: 1rem;
    width: 90%;
    margin: 0 auto;
}

/* Header styling with responsive typography */
.header-content {
    padding: 1rem;
    gap: 0.5rem;
}

.header-content h1 {
    font-size: 1.125rem;
    line-height: 1.2;
}

.header-content .text-orange {
    color: var(--text-orange);
    font-size: 1rem;
}

/* Content section */
.content-section {
    width: 100%;
    margin-top: 4rem;
}

.text-reveal-container {
    max-width: 20rem;
}

.reveal-lines-wrapper {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

/* Text reveal line animations */
.text-reveal-line {
    transition: all 0.7s ease-out;
    opacity: 0.3;
    transform: translateY(1rem);
}

.text-reveal-line.active {
    opacity: 1;
    transform: translateY(0);
}

/* Responsive typography for text lines */
.text-reveal-line:nth-child(1) span,
.text-reveal-line:nth-child(2) span {
    font-size: 1.5rem;
    font-weight: 500;
    line-height: 1.25;
}

.text-reveal-line:nth-child(10) span,
.text-reveal-line:nth-child(11) span {
    font-size: 1.25rem;
    font-weight: 500;
    line-height: 1.25;
}

.text-reveal-line:not(:nth-child(1)):not(:nth-child(2)):not(:nth-child(10)):not(:nth-child(11)) span {
    font-size: 1.125rem;
    font-weight: 500;
    line-height: 1.5;
}

/* Gradient text effects */
.gradient-text-primary {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
}

.gradient-text-blue-purple {
    background: var(--gradient-blue-purple);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
}

.gradient-text-green-blue {
    background: var(--gradient-green-blue);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
}

/* App Store Badges */
.app-store-badges {
    margin-top: 2rem;
}

.store-button {
    background-color: var(--store-bg);
    backdrop-filter: blur(8px);
    border-radius: 6px;
    width: 100%;
    height: 42px;
    padding: 0.375rem 0.75rem;
    cursor: pointer;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.store-button:hover {
    background-color: #ffffff;
    transform: translateY(-1px);
}

.store-text {
    font-size: 0.875rem;
    font-weight: 400;
    white-space: nowrap;
}

.store-icon-container {
    width: 26px;
    height: 26px;
    background-color: var(--store-icon-bg);
    border-radius: 4px;
}

.store-icon {
    width: 1rem;
    height: 1rem;
}

/* Phone mockup */
.phone-mockup {
    max-width: 280px;
    width: 100%;
}

/* Bottom gradient */
.bottom-gradient {
    height: 6rem;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.2), transparent);
}

/* Responsive breakpoints */

/* Extra small devices (portrait phones, less than 576px) */
@media (max-width: 575.98px) {
    .text-reveal-section {
        padding: 1.5rem;
    }
    
    .header-content h1 {
        font-size: 1.25rem;
    }
    
    .header-content .text-orange {
        font-size: 1.125rem;
    }
    
    .reveal-lines-wrapper {
        gap: 1rem;
    }
    
    .text-reveal-line:nth-child(1) span,
    .text-reveal-line:nth-child(2) span {
        font-size: 1.875rem;
    }
    
    .text-reveal-line:nth-child(10) span,
    .text-reveal-line:nth-child(11) span {
        font-size: 1.5rem;
    }
    
    .text-reveal-line:not(:nth-child(1)):not(:nth-child(2)):not(:nth-child(10)):not(:nth-child(11)) span {
        font-size: 1.25rem;
    }
    
    .text-reveal-container {
        max-width: 24rem;
    }
    
    .phone-mockup {
        max-width: 320px;
    }
    
    .store-button {
        width: 150px;
    }
    
    .store-text {
        font-size: 1rem;
    }
    
    .store-icon-container {
        width: 30px;
        height: 30px;
    }
    
    .store-icon {
        width: 1.5rem;
        height: 1.5rem;
    }
}

/* Small devices (landscape phones, 576px and up) */
@media (min-width: 576px) {
    .text-reveal-section {
        padding: 2rem;
    }
    
    .header-content {
        padding: 1.5rem;
    }
    
    .header-content h1 {
        font-size: 1.5rem;
    }
    
    .header-content .text-orange {
        font-size: 1.25rem;
    }
    
    .reveal-lines-wrapper {
        gap: 1.25rem;
    }
    
    .text-reveal-line:nth-child(1) span,
    .text-reveal-line:nth-child(2) span {
        font-size: 2.25rem;
    }
    
    .text-reveal-line:nth-child(10) span,
    .text-reveal-line:nth-child(11) span {
        font-size: 1.875rem;
    }
    
    .text-reveal-line:not(:nth-child(1)):not(:nth-child(2)):not(:nth-child(10)):not(:nth-child(11)) span {
        font-size: 1.5rem;
    }
    
    .text-reveal-container {
        max-width: 28rem;
    }
    
    .phone-mockup {
        max-width: 360px;
    }
    
    .store-button {
        width: 163px;
    }
}

/* Medium devices (tablets, 768px and up) */
@media (min-width: 768px) {
    .text-reveal-section {
        padding: 3rem;
    }
    
    .content-section {
        width: 50%;
        margin-top: 6rem;
    }
    
    .phone-mockup-section {
        width: 50%;
        margin-top: 0;
    }
    
    .header-content h1 {
        font-size: 1.875rem;
    }
    
    .header-content .text-orange {
        font-size: 1.5rem;
    }
    
    .reveal-lines-wrapper {
        gap: 1.5rem;
    }
    
    .text-reveal-line:nth-child(1) span,
    .text-reveal-line:nth-child(2) span {
        font-size: 3rem;
    }
    
    .text-reveal-line:nth-child(10) span,
    .text-reveal-line:nth-child(11) span {
        font-size: 2.25rem;
    }
    
    .text-reveal-line:not(:nth-child(1)):not(:nth-child(2)):not(:nth-child(10)):not(:nth-child(11)) span {
        font-size: 1.875rem;
    }
    
    .text-reveal-container {
        max-width: 32rem;
    }
    
    .phone-mockup {
        max-width: 380px;
    }
}

/* Large devices (desktops, 992px and up) */
@media (min-width: 992px) {
    .text-reveal-section {
        padding: 4rem;
    }
    
    .content-section {
        width: 55%;
        margin-top: 7rem;
    }
    
    .phone-mockup-section {
        width: 45%;
    }
    
    .header-content h1 {
        font-size: 2.25rem;
    }
    
    .header-content .text-orange {
        font-size: 1.875rem;
    }
    
    .text-reveal-line:nth-child(1) span,
    .text-reveal-line:nth-child(2) span {
        font-size: 3.75rem;
    }
    
    .text-reveal-line:nth-child(10) span,
    .text-reveal-line:nth-child(11) span {
        font-size: 3rem;
    }
    
    .text-reveal-line:not(:nth-child(1)):not(:nth-child(2)):not(:nth-child(10)):not(:nth-child(11)) span {
        font-size: 2.25rem;
    }
    
    .text-reveal-container {
        max-width: 36rem;
    }
    
    .phone-mockup {
        max-width: 420px;
    }
}

/* Extra large devices (large desktops, 1200px and up) */
@media (min-width: 1200px) {
    .header-content h1 {
        font-size: 3rem;
    }
    
    .header-content .text-orange {
        font-size: 2.25rem;
    }
    
    .content-section {
        margin-top: 8rem;
    }
    
    .text-reveal-line:nth-child(1) span,
    .text-reveal-line:nth-child(2) span {
        font-size: 4.5rem;
    }
    
    .text-reveal-line:nth-child(10) span,
    .text-reveal-line:nth-child(11) span {
        font-size: 3.75rem;
    }
    
    .text-reveal-line:not(:nth-child(1)):not(:nth-child(2)):not(:nth-child(10)):not(:nth-child(11)) span {
        font-size: 3rem;
    }
    
    .text-reveal-container {
        max-width: 48rem;
    }
}

/* Extra extra large devices (larger desktops, 1400px and up) */
@media (min-width: 1400px) {
    .header-content h1 {
        font-size: 3.75rem;
    }
    
    .header-content .text-orange {
        font-size: 3rem;
    }
    
    .content-section {
        margin-top: 8rem;
    }
    
    .text-reveal-line:nth-child(1) span,
    .text-reveal-line:nth-child(2) span {
        font-size: 4.5rem;
    }
    
    .text-reveal-line:nth-child(10) span,
    .text-reveal-line:nth-child(11) span {
        font-size: 3.75rem;
    }
    
    .text-reveal-line:not(:nth-child(1)):not(:nth-child(2)):not(:nth-child(10)):not(:nth-child(11)) span {
        font-size: 3rem;
    }
    
    .text-reveal-container {
        max-width: 64rem;
    }
}

/* Animation performance optimizations */
.text-reveal-line {
    will-change: opacity, transform;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .text-reveal-line {
        transition: opacity 0.3s ease;
        transform: none !important;
    }
    
    .store-button:hover {
        transform: none;
    }
}