 fetch('components/header/header.html')
  .then(res => res.text())
  .then(data => {
    document.getElementById('header').innerHTML = data;
  });



 fetch('components/Terms/Terms_of_Use.html')
  .then(res => res.text())
  .then(data => {
    document.getElementById('Terms_of_Use').innerHTML = data;
  });

 
fetch('components/footer/footer.html')
  .then(res => res.text())
  .then(data => {
    document.getElementById('footer').innerHTML = data;
  });

 
