
/* ----------------------------------- About-Us-Hero ----------------------------------- */


/* Custom CSS for Flowkar Hero Section */

/* Font Family */
body {
    font-family: 'Figtree', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Hero Container */
.hero-container {
    margin-top: 120px;
    width: 90%;
    margin-left: auto;
    margin-right: auto;
}

/* Hero Header */
.hero-header {
    background-color: #5a3a32 !important;
    background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQ0MCIgaGVpZ2h0PSI0MDAiIHZpZXdCb3g9IjAgMCAxNDQwIDQwMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjE0NDAiIGhlaWdodD0iNDAwIiBmaWxsPSIjNWEzYTMyIi8+CjxwYXRoIGQ9Ik0wIDQwMEwxNDQwIDQwMFYwTDAgNDBaIiBmaWxsPSJ1cmwoI3BhaW50MF9saW5lYXJfMF8xKSIgZmlsbC1vcGFjaXR5PSIwLjEiLz4KPGRlZnM+CjxsaW5lYXJHcmFkaWVudCBpZD0icGFpbnQwX2xpbmVhcl8wXzEiIHgxPSIwIiB5MT0iMCIgeDI9IjE0NDAiIHkyPSI0MDAiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIj4KPHN0b3Agc3RvcC1jb2xvcj0iIzY3NTA0QyIvPgo8c3RvcCBvZmZzZXQ9IjEiIHN0b3AtY29sb3I9IiM2NzUwNEMiIHN0b3Atb3BhY2l0eT0iMCIvPgo8L2xpbmVhckdyYWRpZW50Pgo8L2RlZnM+Cjwvc3ZnPgo=');
    background-size: cover;
    background-position: center;
    border-radius: 12px;
    margin-top: 32px;
    padding: 32px 16px !important;
    gap: 20px !important;
}

/* Hero Title */
.hero-title {
    font-size: 1.5rem;
    font-weight: 600;
    line-height: 1.2;
}

/* Hero Subtitle */
.hero-subtitle {
    color: rgba(255, 255, 255, 0.6);
    font-size: 1rem;
    font-weight: 400;
    max-width: 32rem;
}

/* <PERSON> Button */
.hero-button {
    background-color: white !important;
    color: black !important;
    border: none;
    border-radius: 6px;
    width: 201px;
    height: 42px;
    padding: 6px 6px 6px 20px !important;
    margin-top: 20px;
    font-weight: 400;
    transition: all 0.3s ease;
}

.hero-button:hover {
    background-color: #f8f9fa !important;
    color: black !important;
    transform: translateY(-1px);
}

.hero-button:focus {
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
}

/* Button Text */
.button-text {
    font-size: 14px;
    font-weight: 400;
}

/* Arrow Icon */
.arrow-icon {
    width: 30px;
    height: 30px;
    flex-shrink: 0;
}

/* Responsive Design */
@media (min-width: 576px) {
    .hero-container {
        margin-top: 80px;
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }
    
    .hero-header {
        border-radius: 16px;
        margin-top: 48px;
        padding: 48px 24px !important;
    }
    
    .hero-title {
        font-size: 1.875rem;
    }
    
    .hero-subtitle {
        font-size: 1.125rem;
    }
}

@media (min-width: 992px) {
    .hero-container {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }
    
    .hero-header {
        border-radius: 20px;
        margin-top: 160px;
        padding: 70px 24px !important;
    }
    
    .hero-title {
        font-size: 2.25rem;
    }
    
    .hero-subtitle {
        font-size: 1.25rem;
    }
}

/* Additional Bootstrap Overrides */
.gap-3 {
    gap: 20px !important;
}

/* Ensure proper spacing */
.hero-header .mb-0 {
    margin-bottom: 0 !important;
}

/* Custom opacity for subtitle */
.opacity-75 {
    opacity: 0.8 !important;
}


















/* ---------------------- About-Us-Cards ---------------------- */

/* Custom CSS for Cards Component */

/* Container styling to match original width and spacing */
.container-custom {
    width: 90%;
    margin: 120px 0;
    padding: 0 1rem;
}

/* Card custom styling */
.card-custom {
    border-radius: 1rem; /* Equivalent to rounded-2xl */
    padding: 2rem 1.5rem;
    border: 1px solid #E3E3E3;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); /* shadow-sm */
    transition: all 0.3s ease;
}

/* Card icon styling */
.card-icon {
    width: 61px;
    height: 60px;
    margin-bottom: 0.5rem;
}

/* Card title styling */
.card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #563D39;
    margin-bottom: 0.5rem;
}

/* Card description styling */
.card-description {
    color: rgba(0, 0, 0, 0.6);
    font-size: 0.875rem;
    font-weight: 400;
    line-height: 1.6;
    opacity: 0.8;
    margin: 0;
}

/* Responsive adjustments */
@media (min-width: 576px) {
    .container-custom {
        padding: 0 1.5rem;
    }
    
    .card-custom {
        padding: 2rem 1.5rem;
    }
    
    .card-title {
        font-size: 1.25rem;
    }
    
    .card-description {
        font-size: 1rem;
    }
}

@media (min-width: 992px) {
    .container-custom {
        padding: 0 2rem;
    }
    
    .card-custom {
        padding: 1.25rem 1.875rem;
    }
}

/* Hover effects (optional enhancement) */
.card-custom:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Ensure equal height cards */
.card-custom {
    height: 100%;
}















/* -----------------  About-Us-TextAndImage -----------------------  */

/* Custom CSS for TextAndImage Component */

/* Font Family */
body {
    font-family: 'Figtree', sans-serif;
}

/* Main Component Container */
.text-image-component {
    background-color: #5C4742;
    border-radius: 40px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 1.5rem;
    width: 90%;
    margin: 2rem auto;
    min-height: 400px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Image Section */
.image-section {
    flex-shrink: 0;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 2rem;
    margin: 0px 20px;
}

.component-image {
    border-radius: 30px;
    object-fit: cover;
    width: 320px;
    height: 320px;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Text Section */
.text-section {
    width: 100%;
    color: white;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.component-title {
    font-size: 1.875rem;
    font-weight: 400;
    margin-bottom: 1.5rem;
    line-height: 1.25;
    color: white;
}

.component-text {
    color: #FFFFFFB2;
    font-size: 1.125rem;
    opacity: 0.7;
    line-height: 1.6;
}

/* Medium screens and up (md breakpoint - 768px+) */
@media (min-width: 768px) {
    .text-image-component {
        flex-direction: row;
        padding: 3rem;
    }
    
    .image-section {
        width: 50%;
        margin-bottom: 0;
        margin-right: 2rem;
    }
    
    .component-image {
        width: 642px;
        height: 514px;
    }
    
    .text-section {
        width: 50%;
    }
    
    .component-title {
        font-size: 3rem;
    }
    
    .component-text {
        font-size: 1.25rem;
    }
}

/* Responsive adjustments for smaller screens */
@media (max-width: 767px) {
    .text-image-component {
        padding: 1.5rem;
    }
    
    .component-image {
        max-width: 100%;
        height: auto;
        aspect-ratio: 1;
    }
}

/* Smooth transitions for responsive behavior */
.text-image-component,
.image-section,
.text-section,
.component-image {
    transition: all 0.3s ease-in-out;
}

/* Additional Bootstrap utility overrides if needed */
.mb-4 {
    margin-bottom: 1rem !important;
}





















/* -----------------  About-Us-Stats -----------------------  */



/* Stats Section Styles */
.stats-section {
    background-color: rgba(86, 61, 57, 0.1); /* #563D391A */
    border-radius: 1.5rem; /* rounded-3xl equivalent */
    padding: 3rem 1rem; /* py-12 px-4 */
    width: 90%;
    margin: 7.5rem auto; /* my-[120px] */
}

/* Stat Item Styles */
.stat-item {
    padding: 1.5rem; /* px-6 py-6 */
}

/* Stat Value Styles */
.stat-value {
    font-size: 2.25rem; /* text-4xl */
    font-weight: 400; /* font-normal */
    color: #563D39;
    line-height: 1.1;
}

/* Stat Label Styles */
.stat-label {
    margin-top: 1rem; /* mt-4 */
    font-size: 1.125rem; /* text-lg */
    color: rgba(86, 61, 57, 0.6); /* #563D3999 */
    font-weight: 300; /* font-light */
    line-height: 1.4;
}

/* Vertical Divider for Desktop */
.vertical-divider {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    height: 106px;
    width: 1px;
    background: linear-gradient(to bottom, #EFECEC, #563D39, #EFECEC);
}

/* Horizontal Divider for Mobile */
.horizontal-divider {
    margin-top: 1.5rem; /* mt-6 */
    width: 60%;
    height: 1px;
    background: linear-gradient(to right, #EFECEC, #563D39, #EFECEC);
}

/* Remove dividers from last item */
.stat-item:last-child .vertical-divider,
.stat-item:last-child .horizontal-divider {
    display: none !important;
}

/* Responsive Design for Medium and Larger Screens */
@media (min-width: 768px) {
    .stat-value {
        font-size: 3rem; /* md:text-5xl */
    }
    
    .stat-label {
        font-size: 1.25rem; /* md:text-xl */
    }
}

/* Additional Bootstrap Flex Utilities */
.flex-md-1 {
    flex: 1;
}

@media (min-width: 768px) {
    .flex-md-1 {
        flex: 1 1 0%;
    }
}

/* Ensure proper spacing and alignment */
body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

/* Fine-tune responsive behavior */
@media (max-width: 767.98px) {
    .stats-section {
        margin: 5rem auto; /* Slightly reduce margin on mobile */
        padding: 2rem 1rem;
    }
    
    .stat-item {
        padding: 1rem 1.5rem;
    }
}
















/* -----------------  About-Us-OurMission -----------------------  */

/* Mission Section Styles */
.mission-section {
  width: 90%;
  margin: 0 auto;
  padding: 3rem 1rem;
}

.mission-container {
  max-width: 100%;
}

/* Header Styles */
.mission-title {
  color: #563d39;
  font-size: 1.5rem;
  font-weight: 600;
  text-align: center;
  margin-bottom: 1.25rem;
}

.mission-description {
  text-align: center;
  color: #00000099;
  margin-bottom: 2rem;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.5;
}

/* Mission Cards Container */
.mission-cards {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Individual Mission Card */
.mission-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
}

/* Mission Image */
.mission-image {
  width: 100%;
  height: 408px;
  object-fit: cover;
  border-radius: 30px;
}

/* Mission Content */
.mission-content {
  flex: 1;
}

.mission-card-title {
  color: #563d39;
  font-size: 34px;
  font-weight: 500;
  margin-bottom: 20px;
}

.mission-card-desc {
  color: #00000099;
  font-weight: 300;
  font-size: 0.875rem;
  line-height: 1.6;
}

/* Responsive Styles for Medium and Large Screens */
@media (min-width: 768px) {
  .mission-title {
    font-size: 1.875rem;
  }

  .mission-description {
    font-size: 1.125rem;
  }

  .mission-card {
    flex-direction: row;
    gap: 60px;
  }

  .mission-image {
    width: 368px;
    flex-shrink: 0;
  }

  .mission-card-desc {
    font-size: 1.125rem;
  }
}

/* Additional responsive adjustments */
@media (max-width: 767px) {
  .mission-section {
    padding: 2rem 1rem;
  }
}

/* Smooth transitions for any hover effects (if needed in future) */
.mission-image,
.mission-card {
  transition: all 0.3s ease;
}

/* Ensure proper spacing and alignment */
.mission-card:last-child {
  margin-bottom: 0;
}

/* Typography consistency */
body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

/* Ensure images are responsive */
.mission-image {
  max-width: 100%;
  height: auto;
  min-height: 408px;
  object-fit: cover;
}

@media (min-width: 768px) {
  .mission-image {
    height: 408px;
    width: 368px;
  }
}














/* -----------------  About-Us-GetStarted -----------------------  */

/* Get Started Section Styles */
.get-started-section {
    margin: 60px auto;
    width: 90%;
    border-radius: 40px;
    background-image: url('https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Blog_bg-jzBpQJ5DjyOrT7A7AU9GRdJs9MPdFI.svg');
    background-size: cover;
    background-position: center;
    padding: 0 1rem;
}

.get-started-content {
    min-height: 400px;
    border-radius: 20px;
    padding: 100px 1rem;
    gap: 20px;
}

.get-started-text {
    color: rgba(255, 255, 255, 0.5);
    font-weight: 400;
    font-size: 16px;
    max-width: 800px;
    padding: 0 1rem;
}

/* Download Buttons */
.download-btn {
    background-color: #FFFFFF;
    backdrop-filter: blur(8px);
    border-radius: 6px;
    width: 150px;
    height: 42px;
    padding: 6px 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
}

.download-btn:hover {
    background-color: #FFFFFF;
    transform: translateY(-1px);
}

.btn-text {
    color: black;
    font-size: 14px;
    font-weight: 400;
    white-space: nowrap;
}

.btn-icon {
    width: 26px;
    height: 26px;
    background-color: rgba(86, 61, 57, 0.2);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 8px;
    flex-shrink: 0;
}

.icon-svg {
    width: 16px;
    height: 16px;
    color: black;
}

/* Floating Images Base Styles */
.floating-img {
    position: absolute;
}

.floating-img img {
    border-radius: 12px;
}

/* Mobile Layout (up to 575px) */
.floating-img-01-mobile {
    top: 20px;
    left: 20px;
}

.floating-img-01-mobile img {
    height: 80px;
    width: 80px;
}

.floating-img-02-mobile {
    top: 10px;
    right: 20px;
}

.floating-img-02-mobile img {
    height: 80px;
    width: 80px;
}

.floating-img-03-mobile {
    top: 170px;
    right: -40px;
}

.floating-img-03-mobile img {
    height: 100px;
    width: 100px;
}

.floating-img-04-mobile {
    bottom: 10px;
    right: 30px;
}

.floating-img-04-mobile img {
    height: 80px;
    width: 70px;
}

.floating-img-05-mobile {
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
}

.floating-img-05-mobile img {
    height: 70px;
    width: 70px;
}

.floating-img-06-mobile {
    bottom: 40px;
    left: 10px;
}

.floating-img-06-mobile img {
    height: 90px;
    width: 90px;
}

/* Tablet Layout (576px to 991px) */
.floating-img-01-tablet {
    top: 30px;
    left: 80px;
}

.floating-img-01-tablet img {
    height: 120px;
    width: 120px;
    border-radius: 15px;
}

.floating-img-02-tablet {
    top: 15px;
    right: 120px;
}

.floating-img-02-tablet img {
    height: 120px;
    width: 120px;
    border-radius: 15px;
}

.floating-img-03-tablet {
    top: 180px;
    right: 30px;
}

.floating-img-03-tablet img {
    height: 120px;
    width: 100px;
    border-radius: 15px;
}

.floating-img-04-tablet {
    bottom: 20px;
    right: 30px;
}

.floating-img-04-tablet img {
    height: 120px;
    width: 100px;
    border-radius: 15px;
}

.floating-img-05-tablet {
    bottom: 30px;
    left: 200px;
}

.floating-img-05-tablet img {
    height: 100px;
    width: 100px;
    border-radius: 15px;
}

.floating-img-06-tablet {
    bottom: 40px;
    left: -40px;
}

.floating-img-06-tablet img {
    height: 140px;
    width: 140px;
    border-radius: 15px;
}

/* Desktop Layout (992px and above) */
.floating-img-01-desktop {
    top: 17px;
    left: 153px;
}

.floating-img-01-desktop img {
    height: 180px;
    width: 180px;
    border-radius: 18px;
}

.floating-img-02-desktop {
    top: -28px;
    right: 274px;
}

.floating-img-02-desktop img {
    height: 180px;
    width: 180px;
    border-radius: 18px;
}

.floating-img-03-desktop {
    top: 249px;
    right: 36px;
}

.floating-img-03-desktop img {
    height: 180px;
    width: 180px;
    border-radius: 18px;
}

.floating-img-04-desktop {
    bottom: -30px;
    right: 140px;
}

.floating-img-04-desktop img {
    height: 180px;
    width: 160px;
    border-radius: 18px;
}

.floating-img-05-desktop {
    bottom: 12px;
    left: 374px;
}

.floating-img-05-desktop img {
    height: 159px;
    width: 159px;
    border-radius: 18px;
}

.floating-img-06-desktop {
    bottom: 108px;
    left: 50px;
}

.floating-img-06-desktop img {
    height: 200px;
    width: 200px;
    border-radius: 18px;
}

/* Responsive Adjustments */
@media (min-width: 576px) {
    .get-started-section {
        padding: 0 1.5rem;
    }
    
    .get-started-content {
        min-height: 500px;
        border-radius: 30px;
        padding: 150px 2rem;
        gap: 30px;
    }
    
    .get-started-text {
        font-size: 20px;
        padding: 0 2rem;
    }
    
    .download-btn {
        width: 163px;
        padding: 6px 20px;
    }
    
    .btn-text {
        font-size: 16px;
    }
    
    .btn-icon {
        width: 30px;
        height: 30px;
        margin-left: 16px;
    }
    
    .icon-svg {
        width: 24px;
        height: 24px;
    }
}

@media (min-width: 768px) {
    .get-started-text {
        font-size: 24px;
    }
}

@media (min-width: 992px) {
    .get-started-section {
        padding: 0;
    }
    
    .get-started-content {
        min-height: 600px;
        border-radius: 40px;
        padding: 220px 0;
        gap: 40px;
    }
    
    .get-started-text {
        font-size: 30px;
        padding: 0;
    }
}

/* Utility Classes */
.flex-grow-1 {
    flex-grow: 1;
}

