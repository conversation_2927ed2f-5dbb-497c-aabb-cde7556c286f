<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cards Component</title>
    <!-- Bootstrap 5.3.x CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- External CSS -->
    <link rel="stylesheet" href="/project-folder/css/About_Us.css">
</head>
<body>
    <div class="w-100 d-flex justify-content-center">
        <div class="container-custom">
            <div class="row g-3">
                <!-- Card 1: Our Vision -->
                <div class="col-12 col-sm-6 col-lg-3">
                    <div class="card-custom d-flex flex-column align-items-center bg-white text-center h-100">
                        <div class="mb-4">
                            <svg width="61" height="60" viewBox="0 0 61 60" fill="none" xmlns="http://www.w3.org/2000/svg" class="card-icon">
                                <rect x="0.5" width="60" height="60" rx="30" fill="#F1EDED"/>
                                <g clip-path="url(#clip0_458_19248)">
                                    <path d="M45.3356 29.4882C43.7572 27.2854 40.2902 23.6909 36.4189 21.7949C34.4369 20.8242 32.4652 20.332 30.5586 20.332C28.6527 20.332 26.6755 20.8239 24.682 21.7939C21.0836 23.5448 17.5217 26.9426 15.6689 29.4819C15.5592 29.6323 15.5 29.8137 15.5 30C15.5 30.1862 15.5592 30.3676 15.6689 30.518C15.7369 30.6111 17.3632 32.8245 19.9857 35.063C22.9503 37.5936 26.6269 39.668 30.5586 39.668C34.4763 39.668 38.1251 37.6083 41.0767 35.0598C43.6708 32.8199 45.2688 30.605 45.3356 30.5119C45.4425 30.3626 45.5 30.1836 45.5 30C45.5 29.8164 45.4425 29.6374 45.3356 29.4882ZM30.5586 37.9219C25.5828 37.9219 20.6096 33.8433 17.4944 30.0003C18.9838 28.1657 24.3838 22.0781 30.5586 22.0781C35.5365 22.0781 40.4554 26.1623 43.513 29.9997C42.0558 31.8258 36.7359 37.9219 30.5586 37.9219Z" fill="#563D39"/>
                                    <path d="M30.5586 23.8477C27.1537 23.8477 24.2891 26.6017 24.2891 30C24.2891 33.3921 27.1464 36.1523 30.5586 36.1523C33.951 36.1523 36.7109 33.3924 36.7109 30C36.7109 26.6076 33.951 23.8477 30.5586 23.8477ZM30.5586 34.4063C28.113 34.4063 26.0352 32.3704 26.0352 29.9883C26.0352 27.6062 28.113 25.5938 30.5586 25.5938C32.9817 25.5938 34.9648 27.5651 34.9648 29.9883C34.9648 32.4114 32.9817 34.4063 30.5586 34.4063Z" fill="#563D39"/>
                                    <path d="M30.5586 27.3633C29.0658 27.3633 27.8047 28.5707 27.8047 30C27.8047 31.4293 29.0658 32.6367 30.5586 32.6367C32.0125 32.6367 33.1953 31.4539 33.1953 30C33.1953 28.5461 32.0125 27.3633 30.5586 27.3633ZM30.5586 30.8906C30.0466 30.8906 29.5625 30.4518 29.5625 30C29.5625 29.5482 30.0466 29.1211 30.5586 29.1211C31.0432 29.1211 31.4375 29.5154 31.4375 30C31.4375 30.4846 31.0432 30.8906 30.5586 30.8906Z" fill="#563D39"/>
                                </g>
                                <defs>
                                    <clipPath id="clip0_458_19248">
                                        <rect width="30" height="30" fill="white" transform="translate(15.5 15)"/>
                                    </clipPath>
                                </defs>
                            </svg>
                        </div>
                        <h3 class="card-title mb-2">Our Vision</h3>
                        <p class="card-description">Reimagining social media-one dashboard, every platform, full control.</p>
                    </div>
                </div>

                <!-- Card 2: Our Mission -->
                <div class="col-12 col-sm-6 col-lg-3">
                    <div class="card-custom d-flex flex-column align-items-center bg-white text-center h-100">
                        <div class="mb-4">
                            <svg width="61" height="60" viewBox="0 0 61 60" fill="none" xmlns="http://www.w3.org/2000/svg" class="card-icon">
                                <rect x="0.5" width="60" height="60" rx="30" fill="#F1EDED"/>
                                <g clip-path="url(#clip0_458_19237)">
                                    <path d="M30.5468 25.7685C30.9321 25.7685 31.2443 25.4562 31.2443 25.071C31.2443 24.6859 30.9321 24.3735 30.5468 24.3735C27.4653 24.3735 24.9672 26.8717 24.9672 29.9534C24.9672 33.0349 27.4653 35.533 30.5468 35.533C33.6285 35.533 36.1266 33.0349 36.1266 29.9534C36.1266 29.568 35.8143 29.2559 35.4292 29.2559C35.044 29.2559 34.7317 29.568 34.7317 29.9534C34.7317 32.2646 32.8582 34.1383 30.5468 34.1383C28.2356 34.1383 26.3619 32.2646 26.3619 29.9534C26.3619 27.642 28.2356 25.7685 30.5468 25.7685Z" fill="#563D39"/>
                                    <path d="M43.8547 25.8106C43.7335 25.4447 43.3387 25.2463 42.9725 25.3674C42.6149 25.4857 42.4158 25.8664 42.5225 26.2276C44.6082 32.8698 40.9147 39.9452 34.2725 42.031C27.6306 44.1168 20.5552 40.4233 18.4694 33.7811C16.3834 27.1389 20.0771 20.0636 26.7191 17.9777C29.1777 17.2057 31.8139 17.2057 34.2725 17.9777C34.6417 18.089 35.0311 17.8801 35.1425 17.511C35.2521 17.1473 35.051 16.7631 34.6898 16.6456C27.3145 14.3316 19.4595 18.4347 17.1456 25.8102C14.8316 33.1855 18.9349 41.0404 26.3102 43.3544C33.6858 45.6684 41.5404 41.5653 43.8544 34.1897C44.7101 31.4623 44.7102 28.5382 43.8546 25.8106H43.8547Z" fill="#563D39"/>
                                    <path d="M31.8752 20.9829C31.8788 20.5858 31.5737 20.2539 31.1777 20.2242C30.9694 20.2064 30.7609 20.1887 30.547 20.1887C25.1541 20.1887 20.7823 24.5605 20.7823 29.9534C20.7823 35.3461 25.1541 39.7179 30.547 39.7179C35.9398 39.7179 40.3115 35.3461 40.3115 29.9534C40.3115 29.7011 40.2962 29.4491 40.2652 29.1987C40.2094 28.8159 39.854 28.5508 39.4711 28.6066C39.0883 28.6621 38.8232 29.0178 38.8788 29.4006C38.8809 29.4151 38.8835 29.4294 38.8864 29.4439C38.9016 29.6129 38.9166 29.7804 38.9166 29.953C38.9168 34.5756 35.1696 38.3227 30.5472 38.3232C25.9247 38.3234 22.1775 34.576 22.1773 29.9536C22.1771 25.3313 25.924 21.5839 30.5466 21.5837H30.5468C30.7199 21.5837 30.8875 21.5986 31.0561 21.6135L31.1868 21.6245C31.5513 21.6401 31.8593 21.3572 31.8747 20.9928C31.8749 20.9893 31.8749 20.9862 31.8752 20.9829Z" fill="#563D39"/>
                                    <path d="M34.7317 22.2812V24.7823L30.0537 29.4602C29.7767 29.7279 29.769 30.1695 30.0367 30.4465C30.3043 30.7235 30.7459 30.7313 31.0229 30.4636C31.0287 30.458 31.0345 30.4523 31.0401 30.4465L35.718 25.7686H38.2191C38.4041 25.7686 38.5814 25.6951 38.7123 25.5642L42.897 21.3793C43.1694 21.1069 43.1694 20.6654 42.897 20.393C42.8323 20.3282 42.7554 20.2769 42.6708 20.2418C42.5862 20.2068 42.4955 20.1888 42.404 20.1888H40.3115V18.0963C40.3115 17.7111 39.9992 17.3988 39.6138 17.399C39.429 17.3991 39.2517 17.4725 39.121 17.6033L34.9361 21.7882C34.8713 21.8529 34.8199 21.9297 34.7848 22.0143C34.7497 22.0989 34.7317 22.1896 34.7317 22.2812ZM36.1267 22.57L38.9165 19.7801V20.8863C38.9165 21.2714 39.2289 21.5837 39.614 21.5837H40.7202L37.9302 24.3736H36.1267V22.57Z" fill="#563D39"/>
                                </g>
                                <defs>
                                    <clipPath id="clip0_458_19237">
                                        <rect width="28" height="28" fill="white" transform="translate(16.5 16)"/>
                                    </clipPath>
                                </defs>
                            </svg>
                        </div>
                        <h3 class="card-title mb-2">Our Mission</h3>
                        <p class="card-description">Unifying platforms. Simplifying chaos. Powering creators without the clutter.</p>
                    </div>
                </div>

                <!-- Card 3: Our Team -->
                <div class="col-12 col-sm-6 col-lg-3">
                    <div class="card-custom d-flex flex-column align-items-center bg-white text-center h-100">
                        <div class="mb-4">
                            <svg width="61" height="60" viewBox="0 0 61 60" fill="none" xmlns="http://www.w3.org/2000/svg" class="card-icon">
                                <rect x="0.5" width="60" height="60" rx="30" fill="#F1EDED"/>
                                <g clip-path="url(#clip0_458_19237_team)">
                                    <path d="M30.5468 25.7685C30.9321 25.7685 31.2443 25.4562 31.2443 25.071C31.2443 24.6859 30.9321 24.3735 30.5468 24.3735C27.4653 24.3735 24.9672 26.8717 24.9672 29.9534C24.9672 33.0349 27.4653 35.533 30.5468 35.533C33.6285 35.533 36.1266 33.0349 36.1266 29.9534C36.1266 29.568 35.8143 29.2559 35.4292 29.2559C35.044 29.2559 34.7317 29.568 34.7317 29.9534C34.7317 32.2646 32.8582 34.1383 30.5468 34.1383C28.2356 34.1383 26.3619 32.2646 26.3619 29.9534C26.3619 27.642 28.2356 25.7685 30.5468 25.7685Z" fill="#563D39"/>
                                    <path d="M43.8547 25.8106C43.7335 25.4447 43.3387 25.2463 42.9725 25.3674C42.6149 25.4857 42.4158 25.8664 42.5225 26.2276C44.6082 32.8698 40.9147 39.9452 34.2725 42.031C27.6306 44.1168 20.5552 40.4233 18.4694 33.7811C16.3834 27.1389 20.0771 20.0636 26.7191 17.9777C29.1777 17.2057 31.8139 17.2057 34.2725 17.9777C34.6417 18.089 35.0311 17.8801 35.1425 17.511C35.2521 17.1473 35.051 16.7631 34.6898 16.6456C27.3145 14.3316 19.4595 18.4347 17.1456 25.8102C14.8316 33.1855 18.9349 41.0404 26.3102 43.3544C33.6858 45.6684 41.5404 41.5653 43.8544 34.1897C44.7101 31.4623 44.7102 28.5382 43.8546 25.8106H43.8547Z" fill="#563D39"/>
                                    <path d="M31.8752 20.9829C31.8788 20.5858 31.5737 20.2539 31.1777 20.2242C30.9694 20.2064 30.7609 20.1887 30.547 20.1887C25.1541 20.1887 20.7823 24.5605 20.7823 29.9534C20.7823 35.3461 25.1541 39.7179 30.547 39.7179C35.9398 39.7179 40.3115 35.3461 40.3115 29.9534C40.3115 29.7011 40.2962 29.4491 40.2652 29.1987C40.2094 28.8159 39.854 28.5508 39.4711 28.6066C39.0883 28.6621 38.8232 29.0178 38.8788 29.4006C38.8809 29.4151 38.8835 29.4294 38.8864 29.4439C38.9016 29.6129 38.9166 29.7804 38.9166 29.953C38.9168 34.5756 35.1696 38.3227 30.5472 38.3232C25.9247 38.3234 22.1775 34.576 22.1773 29.9536C22.1771 25.3313 25.924 21.5839 30.5466 21.5837H30.5468C30.7199 21.5837 30.8875 21.5986 31.0561 21.6135L31.1868 21.6245C31.5513 21.6401 31.8593 21.3572 31.8747 20.9928C31.8749 20.9893 31.8749 20.9862 31.8752 20.9829Z" fill="#563D39"/>
                                    <path d="M34.7317 22.2812V24.7823L30.0537 29.4602C29.7767 29.7279 29.769 30.1695 30.0367 30.4465C30.3043 30.7235 30.7459 30.7313 31.0229 30.4636C31.0287 30.458 31.0345 30.4523 31.0401 30.4465L35.718 25.7686H38.2191C38.4041 25.7686 38.5814 25.6951 38.7123 25.5642L42.897 21.3793C43.1694 21.1069 43.1694 20.6654 42.897 20.393C42.8323 20.3282 42.7554 20.2769 42.6708 20.2418C42.5862 20.2068 42.4955 20.1888 42.404 20.1888H40.3115V18.0963C40.3115 17.7111 39.9992 17.3988 39.6138 17.399C39.429 17.3991 39.2517 17.4725 39.121 17.6033L34.9361 21.7882C34.8713 21.8529 34.8199 21.9297 34.7848 22.0143C34.7497 22.0989 34.7317 22.1896 34.7317 22.2812ZM36.1267 22.57L38.9165 19.7801V20.8863C38.9165 21.2714 39.2289 21.5837 39.614 21.5837H40.7202L37.9302 24.3736H36.1267V22.57Z" fill="#563D39"/>
                                </g>
                                <defs>
                                    <clipPath id="clip0_458_19237_team">
                                        <rect width="28" height="28" fill="white" transform="translate(16.5 16)"/>
                                    </clipPath>
                                </defs>
                            </svg>
                        </div>
                        <h3 class="card-title mb-2">Our Team</h3>
                        <p class="card-description">Thinkers, builders, dreamers—engineering simplicity for the social world.</p>
                    </div>
                </div>

                <!-- Card 4: Our Story -->
                <div class="col-12 col-sm-6 col-lg-3">
                    <div class="card-custom d-flex flex-column align-items-center bg-white text-center h-100">
                        <div class="mb-4">
                            <svg width="61" height="60" viewBox="0 0 61 60" fill="none" xmlns="http://www.w3.org/2000/svg" class="card-icon">
                                <rect x="0.5" width="60" height="60" rx="30" fill="#F1EDED"/>
                                <g clip-path="url(#clip0_458_19275)">
                                    <path d="M40.3993 20.1009C36.4332 16.1383 30.4872 14.9206 25.2827 17.0051C24.787 17.203 24.5455 17.7653 24.7433 18.261C24.9412 18.7567 25.5035 18.9982 25.9992 18.8003L26.0034 18.7986C27.4328 18.2244 28.9594 17.9306 30.4999 17.9333C37.1536 17.9333 42.5668 23.3465 42.5668 30.0002C42.557 34.2956 40.2744 38.2648 36.5667 40.4336C36.1044 40.7004 35.946 41.2915 36.2128 41.7537C36.4796 42.216 37.0707 42.3745 37.5329 42.1077L37.54 42.1035C41.8505 39.5971 44.5017 34.9864 44.4997 30.0002C44.5047 28.161 44.1449 26.339 43.4411 24.6398C42.7372 22.9406 41.7034 21.3978 40.3993 20.1009ZM18.5651 24.6664C19.0343 24.9201 19.6203 24.7454 19.874 24.2762C20.7382 22.6816 21.9474 21.3 23.4133 20.2321C23.8463 19.9199 23.9442 19.3159 23.6321 18.8829C23.3199 18.45 22.7159 18.352 22.2829 18.6642L22.277 18.6685C20.5775 19.9066 19.1757 21.5083 18.1736 23.3568C17.9201 23.8265 18.0953 24.4128 18.5651 24.6664C18.565 24.6664 18.565 24.6664 18.5651 24.6664ZM18.8909 33.305C18.3509 31.3924 18.2875 29.3766 18.7063 27.4338C18.8165 26.9115 18.4826 26.3988 17.9604 26.2885C17.4419 26.1791 16.932 26.5075 16.8172 27.0248C16.3313 29.2776 16.4049 31.6152 17.0316 33.833C17.1774 34.3464 17.7119 34.6444 18.2253 34.4986C18.7388 34.3527 19.0368 33.8182 18.8909 33.3048V33.305ZM23.7895 40.0308C22.2226 38.978 20.9218 37.5755 19.9899 35.9339C19.7329 35.466 19.1454 35.2951 18.6775 35.552C18.2097 35.809 18.0387 36.3966 18.2957 36.8644C19.3769 38.777 20.8894 40.411 22.713 41.6363C23.1564 41.9335 23.7567 41.815 24.0539 41.3717C24.3512 40.9283 24.2327 40.328 23.7893 40.0308H23.7895ZM34.0383 41.5402C31.3995 42.3445 28.5653 42.2251 26.0035 41.2018C25.5082 41.0028 24.9454 41.243 24.7464 41.7382C24.5474 42.2335 24.7876 42.7963 25.2828 42.9953C28.2547 44.1834 31.5429 44.322 34.6042 43.3885C35.1146 43.2322 35.4017 42.6918 35.2454 42.1814C35.0891 41.671 34.5487 41.3839 34.0383 41.5402Z" fill="#563D39"/>
                                    <path d="M30.5 22.4303C29.9662 22.4303 29.5335 22.863 29.5335 23.3968V29.0338H23.8965C23.3628 29.0338 22.9301 29.4665 22.9301 30.0003C22.9301 30.534 23.3627 30.9667 23.8965 30.9667H29.5335V36.6037C29.5335 37.1375 29.9662 37.5702 30.5 37.5702C31.0337 37.5702 31.4664 37.1375 31.4664 36.6037V30.9666H37.1034C37.6372 30.9666 38.0699 30.5339 38.0699 30.0001C38.0699 29.4664 37.6372 29.0337 37.1034 29.0337H31.4664V23.3967C31.4664 22.863 31.0337 22.4303 30.5 22.4303Z" fill="#563D39"/>
                                </g>
                                <defs>
                                    <clipPath id="clip0_458_19275">
                                        <rect width="28" height="28" fill="white" transform="translate(16.5 16)"/>
                                    </clipPath>
                                </defs>
                            </svg>
                        </div>
                        <h3 class="card-title mb-2">Our Story</h3>
                        <p class="card-description">From chaos came clarity. Flowkar was our turning point.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5.3.x JS (Optional - only needed for interactive components) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>