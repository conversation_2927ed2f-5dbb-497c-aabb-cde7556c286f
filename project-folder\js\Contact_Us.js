// JavaScript for loading state management
document.addEventListener('DOMContentLoaded', function() {
    const heroSkeleton = document.getElementById('hero-skeleton');
    const heroMain = document.getElementById('hero-main');
    
    // Simulate loading for 1 second (1000ms) like the original React component
    setTimeout(function() {
        // Hide skeleton
        heroSkeleton.style.display = 'none';
        
        // Show main hero content
        heroMain.classList.remove('d-none');
        heroMain.classList.add('d-block');
    }, 1000);
});

// Optional: Add smooth fade transition
function fadeTransition() {
    const heroSkeleton = document.getElementById('hero-skeleton');
    const heroMain = document.getElementById('hero-main');
    
    setTimeout(function() {
        heroSkeleton.style.opacity = '0';
        
        setTimeout(function() {
            heroSkeleton.style.display = 'none';
            heroMain.classList.remove('d-none');
            heroMain.style.opacity = '0';
            
            setTimeout(function() {
                heroMain.style.opacity = '1';
            }, 50);
        }, 300);
    }, 1000);
}

// Uncomment the line below if you want smooth fade transitions
// fadeTransition();
