<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Get Started - Flowkar</title>

    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <link rel="stylesheet" href="/project-folder/css/About_Us.css">
</head>
<body>
    <div class="container-fluid px-0">
        <div class="get-started-section mx-auto">

            <div class="position-relative get-started-content d-flex flex-column justify-content-center align-items-center text-center">
                

                <p class="get-started-text mb-0">
                    1000+ people like you use Flowkar to build their brand on social media every month
                </p>
                
                <div class="d-flex flex-column flex-sm-row gap-3 mt-4 mt-sm-5 justify-content-center align-items-center">
  
                    <div class="download-btn" onclick="handleRedirectAppStore()">
                        <div class="flex-grow-1 text-start">
                            <div class="btn-text">App Store</div>
                        </div>
                        <div class="btn-icon">
                            <svg class="icon-svg" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z" />
                            </svg>
                        </div>
                    </div>

                    <div class="download-btn" onclick="handleRedirectPlayStore()">
                        <div class="flex-grow-1 text-start">
                            <div class="btn-text">Google Play</div>
                        </div>
                        <div class="btn-icon">
                            <svg class="icon-svg" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.61 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.9 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z" />
                            </svg>
                        </div>
                    </div>
                </div>

                 <!-- Floating Images - Mobile Layout  -->
                <div class="floating-images d-block d-sm-none">
                    <div class="floating-img floating-img-01-mobile">
                        <img src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/GetStarted_01-1QYSPI2idhvSryBy49eEjZt5X9usV8.svg" alt="Image_01" class="img-fluid">
                    </div>
                    <div class="floating-img floating-img-02-mobile">
                        <img src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/GetStarted_02-v2NxbUPCvmKGD7ShZj4E9oMWEmIpy2.svg" alt="Image_02" class="img-fluid">
                    </div>
                    <div class="floating-img floating-img-03-mobile">
                        <img src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/GetStarted_03-cjnVdH5ACJiQxBByR9yc90GxawfjFK.svg" alt="Image_03" class="img-fluid">
                    </div>
                    <div class="floating-img floating-img-04-mobile">
                        <img src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/GetStarted_04-b4wdgWVsYmY2oPQsXSlDIsg0RWzX0y.svg" alt="Image_04" class="img-fluid">
                    </div>
                    <div class="floating-img floating-img-05-mobile">
                        <img src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/GetStarted_05-KqCad3dqQlB8IAomGfruerzrh52asT.svg" alt="Image_05" class="img-fluid">
                    </div>
                    <div class="floating-img floating-img-06-mobile">
                        <img src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/GetStarted_06-UDL4a50msPByho3yX0zJoXrHB801xj.svg" alt="Image_06" class="img-fluid">
                    </div>
                </div>

                 <!-- Floating Images - Tablet Layout  -->
                <div class="floating-images d-none d-sm-block d-lg-none">
                    <div class="floating-img floating-img-01-tablet">
                        <img src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/GetStarted_01-1QYSPI2idhvSryBy49eEjZt5X9usV8.svg" alt="Image_01" class="img-fluid">
                    </div>
                    <div class="floating-img floating-img-02-tablet">
                        <img src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/GetStarted_02-v2NxbUPCvmKGD7ShZj4E9oMWEmIpy2.svg" alt="Image_02" class="img-fluid">
                    </div>
                    <div class="floating-img floating-img-03-tablet">
                        <img src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/GetStarted_03-cjnVdH5ACJiQxBByR9yc90GxawfjFK.svg" alt="Image_03" class="img-fluid">
                    </div>
                    <div class="floating-img floating-img-04-tablet">
                        <img src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/GetStarted_04-b4wdgWVsYmY2oPQsXSlDIsg0RWzX0y.svg" alt="Image_04" class="img-fluid">
                    </div>
                    <div class="floating-img floating-img-05-tablet">
                        <img src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/GetStarted_05-KqCad3dqQlB8IAomGfruerzrh52asT.svg" alt="Image_05" class="img-fluid">
                    </div>
                    <div class="floating-img floating-img-06-tablet">
                        <img src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/GetStarted_06-UDL4a50msPByho3yX0zJoXrHB801xj.svg" alt="Image_06" class="img-fluid">
                    </div>
                </div>

                 <!-- Floating Images - Desktop Layout  -->
                <div class="floating-images d-none d-lg-block">
                    <div class="floating-img floating-img-01-desktop">
                        <img src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/GetStarted_01-1QYSPI2idhvSryBy49eEjZt5X9usV8.svg" alt="Image_01" class="img-fluid">
                    </div>
                    <div class="floating-img floating-img-02-desktop">
                        <img src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/GetStarted_02-v2NxbUPCvmKGD7ShZj4E9oMWEmIpy2.svg" alt="Image_02" class="img-fluid">
                    </div>
                    <div class="floating-img floating-img-03-desktop">
                        <img src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/GetStarted_03-cjnVdH5ACJiQxBByR9yc90GxawfjFK.svg" alt="Image_03" class="img-fluid">
                    </div>
                    <div class="floating-img floating-img-04-desktop">
                        <img src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/GetStarted_04-b4wdgWVsYmY2oPQsXSlDIsg0RWzX0y.svg" alt="Image_04" class="img-fluid">
                    </div>
                    <div class="floating-img floating-img-05-desktop">
                        <img src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/GetStarted_05-KqCad3dqQlB8IAomGfruerzrh52asT.svg" alt="Image_05" class="img-fluid">
                    </div>
                    <div class="floating-img floating-img-06-desktop">
                        <img src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/GetStarted_06-UDL4a50msPByho3yX0zJoXrHB801xj.svg" alt="Image_06" class="img-fluid">
                    </div>
                </div>
            </div>
        </div>
    </div>


    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    

    <script>
        function handleRedirectPlayStore() {
            window.location.href = "https://play.google.com/store/apps/details?id=com.app.flowkar&hl=en";
        }

        function handleRedirectAppStore() {
            window.location.href = "https://apps.apple.com/in/app/flowkar/id6740058663";
        }
    </script>
</body>
</html>