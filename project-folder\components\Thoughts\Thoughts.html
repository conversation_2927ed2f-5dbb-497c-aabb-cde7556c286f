<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thoughts That Move You</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/project-folder/css/Thoughts.css">
</head>
<body>
    <div class="thoughts-container">
        <div class="thoughts-inner">
            <!-- Header -->
            <div class="row justify-content-between align-items-center text-center mb-4 mb-md-5 g-3">
                <div class="col-12 col-md-6 order-1">
                    <h2 class="thoughts-title mb-2 mb-md-4">Thoughts That Move You.</h2>
                </div>
                <div class="col-12 col-md-6 order-2">
                    <p class="thoughts-subtitle">Stories that stay with you</p>
                </div>
            </div>

            <!-- Loading State (hidden by default) -->
            <div id="loadingGrid" class="thoughts-grid d-none">
                <div class="grid-item large-card">
                    <div class="skeleton-card">
                        <div class="skeleton-gradient"></div>
                        <div class="skeleton-content">
                            <div class="skeleton-line skeleton-line-lg"></div>
                            <div class="skeleton-line skeleton-line-md"></div>
                            <div class="skeleton-line skeleton-line-sm"></div>
                        </div>
                    </div>
                </div>
                <div class="grid-item landscape-card">
                    <div class="skeleton-card">
                        <div class="skeleton-gradient"></div>
                        <div class="skeleton-play-button">
                            <div class="skeleton-play-outer">
                                <div class="skeleton-play-inner"></div>
                            </div>
                        </div>
                        <div class="skeleton-content">
                            <div class="skeleton-line skeleton-line-lg"></div>
                        </div>
                    </div>
                </div>
                <div class="grid-item small-card-1">
                    <div class="skeleton-card">
                        <div class="skeleton-gradient"></div>
                        <div class="skeleton-content">
                            <div class="skeleton-line skeleton-line-md"></div>
                        </div>
                    </div>
                </div>
                <div class="grid-item small-card-2">
                    <div class="skeleton-card">
                        <div class="skeleton-gradient"></div>
                        <div class="skeleton-content">
                            <div class="skeleton-line skeleton-line-md"></div>
                        </div>
                    </div>
                </div>
                <div class="grid-item bottom-left-card">
                    <div class="skeleton-card">
                        <div class="skeleton-gradient"></div>
                        <div class="skeleton-play-button">
                            <div class="skeleton-play-outer">
                                <div class="skeleton-play-inner"></div>
                            </div>
                        </div>
                        <div class="skeleton-content">
                            <div class="skeleton-line skeleton-line-lg"></div>
                        </div>
                    </div>
                </div>
                <div class="grid-item bottom-card-1">
                    <div class="skeleton-card">
                        <div class="skeleton-gradient"></div>
                        <div class="skeleton-content">
                            <div class="skeleton-line skeleton-line-md"></div>
                        </div>
                    </div>
                </div>
                <div class="grid-item bottom-card-2">
                    <div class="skeleton-card">
                        <div class="skeleton-gradient"></div>
                        <div class="skeleton-content">
                            <div class="skeleton-line skeleton-line-md"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content Grid -->
            <div id="contentGrid" class="thoughts-grid">
                <!-- Large Card - Top Left -->
                <div class="grid-item large-card">
                    <div class="blog-card h-100 d-flex flex-column" data-blog-id="1">
                        <div class="card-image-container flex-grow-1">
                            <img src="/project-folder/assets/Thoughts/Dummy_01.svg" 
                                 alt="A Day in the Life of an Advanced Humanity's Open Window to Space" 
                                 class="card-image" loading="lazy">
                            <div class="card-overlay"></div>
                        </div>
                        <div class="card-content-large">
                            <h3 class="card-title-large">A Day in the Life of an Advanced Humanity's Open Window to Space</h3>
                            <p class="card-subtitle">Artificial Intelligence with the Intention of freedom related to space and time</p>
                            <p class="card-description">Exploring the vast cosmos with the freedom of thought and the intention to expand our understanding of the universe.</p>
                        </div>
                    </div>
                </div>

                <!-- Top Right - Large Landscape Card -->
                <div class="grid-item landscape-card">
                    <div class="blog-card h-100" data-blog-id="2">
                        <img src="/project-folder/assets/Thoughts/Dummy_01.svg" 
                             alt="Echoes Through The Land" 
                             class="card-image">
                        <div class="card-overlay"></div>
                        <div class="card-content">
                            <h3 class="card-title">Echoes Through The Land</h3>
                        </div>
                    </div>
                </div>

                <!-- Small Card 1 -->
                <div class="grid-item small-card-1">
                    <div class="blog-card h-100" data-blog-id="3">
                        <img src="/project-folder/assets/Thoughts/Dummy_01.svg" 
                             alt="Nora Robson" 
                             class="card-image">
                        <div class="card-overlay"></div>
                        <div class="card-content">
                            <h3 class="card-title-small">Nora Robson</h3>
                        </div>
                    </div>
                </div>

                <!-- Small Card 2 -->
                <div class="grid-item small-card-2">
                    <div class="blog-card h-100" data-blog-id="4">
                        <img src="/project-folder/assets/Thoughts/Dummy_01.svg" 
                             alt="The Great Gatsby" 
                             class="card-image">
                        <div class="card-overlay"></div>
                        <div class="card-content">
                            <h3 class="card-title-small">The Great Gatsby</h3>
                        </div>
                    </div>
                </div>

                <!-- Bottom Left Card -->
                <div class="grid-item bottom-left-card">
                    <div class="blog-card h-100" data-blog-id="5">
                        <img src="/project-folder/assets/Thoughts/Dummy_01.svg" 
                             alt="Spiritual Whispers" 
                             class="card-image">
                        <div class="card-overlay"></div>
                        <div class="play-button">
                            <div class="play-button-outer">
                                <div class="play-button-inner">
                                    <svg class="play-icon" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M8 5v14l11-7z" />
                                    </svg>
                                </div>
                            </div>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">Spiritual Whispers</h3>
                        </div>
                    </div>
                </div>

                <!-- Bottom Card 1 -->
                <div class="grid-item bottom-card-1">
                    <div class="blog-card h-100" data-blog-id="6">
                        <img src="/project-folder/assets/Thoughts/Dummy_01.svg" 
                             alt="Ambergris" 
                             class="card-image">
                        <div class="card-overlay"></div>
                        <div class="card-content">
                            <h3 class="card-title">Ambergris</h3>
                        </div>
                    </div>
                </div>

                <!-- Bottom Card 2 -->
                <div class="grid-item bottom-card-2">
                    <div class="blog-card h-100" data-blog-id="7">
                        <img src="/project-folder/assets/Thoughts/Dummy_01.svg" 
                             alt="Culture King" 
                             class="card-image">
                        <div class="card-overlay"></div>
                        <div class="card-content">
                            <h3 class="card-title">Culture King</h3>
                        </div>
                    </div>
                </div>
            </div>

            <!-- View All Button -->
            <div class="d-flex justify-content-center mt-4">
                <button class="btn view-all-btn d-flex align-items-center justify-content-between" onclick="viewAllBlogs()">
                    <span>View All</span>
                    <svg class="arrow-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="script.js"></script>
</body>
</html>