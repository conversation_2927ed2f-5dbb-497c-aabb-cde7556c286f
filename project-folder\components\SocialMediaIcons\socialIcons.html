<!-- Social Media Icons Component Styles -->
<style>
    /* Scoped styles for social media icons component */
    #socialIcons {
        --primary-text: #563D39;
        --secondary-text: #00000099;
        --bg-white: #FFFFFF;
        --youtube-color: #FF0000;
        --reddit-color: #FF4500;
        --instagram-color: #E4405F;
        --facebook-color: #1877F2;
        --twitter-color: #000000;
        --pinterest-color: #BD081C;
        --linkedin-color: #0A66C2;
        --threads-color: #000000;
        --border-inactive: #d1d5db;
        --shadow-light: rgba(0, 0, 0, 0.1);
        --shadow-medium: rgba(0, 0, 0, 0.04);
    }

    #socialIcons .social-slideshow-section {
        width: 100%;
        padding: 144px 16px;
        background-color: var(--bg-white);
        position: relative;
        overflow: hidden;
        display: flex;
        align-items: center;
        min-height: 100vh;
        font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON>', <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, sans-serif;
        line-height: 1.6;
    }

    #socialIcons .section-content {
        text-align: center;
        position: relative;
        z-index: 10;
        width: 100%;
        margin-top: -20px;
    }

    #socialIcons .main-title {
        font-size: 43px;
        font-weight: 500;
        color: var(--primary-text);
        margin-bottom: 24px;
        line-height: 1.2;
    }

    #socialIcons .subtitle {
        min-height: 10vh;
        font-size: 24px;
        color: var(--secondary-text);
        max-width: 768px;
        margin: 0 auto 78px auto;
        font-weight: 300;
    }

    #socialIcons .icons-container {
        position: relative;
    }

    #socialIcons .background-images {
        position: absolute;
        inset: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        pointer-events: none;
    }

    #socialIcons .background-shadow,
    #socialIcons .background-overlay {
        position: absolute;
        width: 583px;
        height: 500px;
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
    }

    #socialIcons .icons-slideshow {
        position: relative;
        overflow: hidden;
        height: 150px;
    }

    #socialIcons .icons-track {
        position: absolute;
        inset: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: opacity 1s ease-in-out;
    }

    #socialIcons .icons-track.restarting {
        opacity: 0.3;
    }

    #socialIcons .social-icon {
        position: absolute;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 9px;
        transition: all 1s ease-in-out;
    }

    #socialIcons .social-icon .icon-container {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 9px !important;
        transition: all 1s ease-in-out;
        background-color: #f8f9fa;
        box-shadow: 0 10px 15px -3px var(--shadow-light), 0 4px 6px -2px var(--shadow-medium);
        flex-shrink: 0;
        width: auto !important;
        height: auto !important;
    }

    #socialIcons .social-icon .icon-container.highlighted {
        box-shadow: 0 20px 25px -5px var(--shadow-light), 0 10px 10px -5px var(--shadow-medium);
    }

    #socialIcons .social-icon img {
        object-fit: contain;
        position: relative;
        z-index: 10;
        transition: all 1s ease-in-out;
        flex-shrink: 0;
    }

    /* Corner brackets */
    #socialIcons .corner-bracket {
        position: absolute;
        width: 24px;
        height: 24px;
        z-index: 20;
        transition: border-color 1s ease-in-out;
        border-color: var(--border-inactive);
    }

    #socialIcons .corner-tl {
        top: -12px;
        left: -12px;
        border-top: 2px solid;
        border-left: 2px solid;
        border-top-left-radius: 10px;
    }

    #socialIcons .corner-tr {
        top: -12px;
        right: -12px;
        border-top: 2px solid;
        border-right: 2px solid;
        border-top-right-radius: 10px;
    }

    #socialIcons .corner-bl {
        bottom: -12px;
        left: -12px;
        border-bottom: 2px solid;
        border-left: 2px solid;
        border-bottom-left-radius: 10px;
    }

    #socialIcons .corner-br {
        bottom: -12px;
        right: -12px;
        border-bottom: 2px solid;
        border-right: 2px solid;
        border-bottom-right-radius: 10px;
    }

    /* Social media icon SVGs */
    #socialIcons .social-svg {
        display: inline-block;
        width: 100%;
        height: 100%;
    }

    /* Responsive styles */
    @media (max-width: 1200px) {
        #socialIcons .main-title {
            font-size: 38px;
        }
        #socialIcons .subtitle {
            font-size: 20px;
        }
    }

    @media (max-width: 768px) {
        #socialIcons .main-title {
            font-size: 30px;
        }
        #socialIcons .subtitle {
            font-size: 16px;
        }
        #socialIcons .icons-slideshow {
            height: 120px;
        }
        #socialIcons .corner-bracket {
            left: -24px !important;
            right: -24px !important;
        }
        #socialIcons .corner-tl, #socialIcons .corner-bl {
            left: -24px;
        }
        #socialIcons .corner-tr, #socialIcons .corner-br {
            right: -24px;
        }
    }

    @media (max-width: 640px) {
        #socialIcons .social-slideshow-section {
            padding: 144px 16px;
        }
    }
</style>
    <!-- Include social media icons SVG definitions -->
    <svg width="0" height="0" style="position: absolute;">
        <defs>
            <!-- YouTube Icon -->
            <g id="youtube">
                ite" d="M19.615 7.154a2.4 2.4 0 0 0-1.69-1.703c-1.492-.4-7.476-.4-7.476-.4s-5.984 0-7.476.4a2.4 2.4 0 0 0-1.69 1.703A25.32 25.32 0 0 0 1 12a25.32 25.32 0 0 0 .283 4.846 2.4 2.4 0 0 0 1.69 1.703c1.492.4 7.476.4 7.476.4s5.984 0 7.476-.4a2.4 2.4 0 0 0 1.69-1.703A25.32 25.32 0 0 0 20 12a25.32 25.32 0 0 0-.385-4.846z"/>
            </g>

            <!-- Reddit Icon -->
            <g id="reddit">
                <circle fill="#FF4500" cx="12" cy="12" r="11"/>
                <path fill="white" d="M12 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0zm5.01 4.744c.688 0 1.25.561 1.25 1.249a1.25 1.25 0 0 1-2.498.056l-2.597-.547-.8 3.747c1.824.07 3.48.632 4.674 1.488.308-.309.73-.491 1.207-.491.968 0 1.754.786 1.754 1.754 0 .716-.435 1.333-1.01 1.614a3.111 3.111 0 0 1 .042.52c0 2.694-3.13 4.87-7.004 4.87-3.874 0-7.004-2.176-7.004-4.87 0-.183.015-.366.043-.534A1.748 1.748 0 0 1 4.028 12c0-.968.786-1.754 1.754-1.754.463 0 .898.196 1.207.49 1.207-.883 2.878-1.43 4.744-1.487l.885-4.182a.342.342 0 0 1 .14-.197.35.35 0 0 1 .238-.042l2.906.617a1.214 1.214 0 0 1 1.108-.701zM9.25 12C8.561 12 8 12.562 8 13.25c0 .687.561 1.248 1.25 1.248.687 0 1.248-.561 1.248-1.249 0-.688-.561-1.249-1.249-1.249zm5.5 0c-.687 0-1.248.561-1.248 1.25 0 .687.561 1.248 1.249 1.248.688 0 1.249-.561 1.249-1.249 0-.687-.562-1.249-1.25-1.249zm-5.466 3.99a.327.327 0 0 0-.231.094.33.33 0 0 0 0 .463c.842.842 2.484.913 2.961.913.477 0 2.105-.056 2.961-.913a.361.361 0 0 0 .029-.463.33.33 0 0 0-.464 0c-.547.533-1.684.73-2.512.73-.828 0-1.979-.196-2.512-.73a.326.326 0 0 0-.232-.095z"/>
            </g>

            <!-- Instagram Icon -->
            <g id="instagram">
                <defs>
                    <linearGradient id="instagram-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#F58529"/>
                        <stop offset="50%" style="stop-color:#DD2A7B"/>
                        <stop offset="100%" style="stop-color:#8134AF"/>
                    </linearGradient>
                </defs>
                <rect fill="url(#instagram-gradient)" width="24" height="24" rx="5.1"/>
                <path fill="white" d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
            </g>

            <!-- Facebook Icon -->
            <g id="facebook">
                <circle fill="#1877F2" cx="12" cy="12" r="12"/>
                <path fill="white" d="M13.5 21.888v-7.077h2.387l.358-2.776H13.5v-1.774c0-.8.223-1.347 1.37-1.347h1.47V6.55a19.56 19.56 0 0 0-2.143-.109c-2.12 0-3.571 1.294-3.571 3.672v2.05H8.238v2.776h2.388v7.077H13.5z"/>
            </g>

            <!-- Twitter/X Icon -->
            <g id="twitter">
                <circle fill="#000000" cx="12" cy="12" r="12"/>
                <path fill="white" d="M18.244 7.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 7.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 9.126H5.117z"/>
            </g>

            <!-- Pinterest Icon -->
            <g id="pinterest">
                <circle fill="#BD081C" cx="12" cy="12" r="12"/>
                <path fill="white" d="M12.017 5C8.396 5 5.029 7.367 5.029 10.987c0 2.079 1.158 3.917 2.618 4.674-.105-.949-.199-2.403.041-3.439.219-.937.906-3.957.906-3.957s-.159-.219-.159-.719c0-.642.362-.995.782-.995.399 0 .637.219.637.642 0 .395-.242 1.237-.369 1.699-.091.408.199.766.588.766.711 0 1.255-.766 1.255-1.599 0-.643-.481-1.299-1.452-1.299-1.013 0-1.597.766-1.597 1.579 0 .259.073.766.231.933.099.159.019.259-.041.381-.051.219-.159.766-.199.876-.061.159-.199.219-.261.129-.641-.259-.921-1.299-.921-1.897 0-.819.821-2.078 2.457-2.078 1.372 0 2.272 1.059 2.272 2.179 0 1.439-.646 2.518-1.577 2.518-.4 0-.748-.219-.898-.479 0 0-.199.757-.241.916-.099.259-.259.757-.399 1.059C9.69 18.761 10.825 19.001 12.017 19.001c3.624 0 6.99-2.367 6.99-6.989C19.007 8.367 15.641 5.001 12.017 5.001z"/>
            </g>

            <!-- LinkedIn Icon -->
            <g id="linkedin">
                <circle fill="#0A66C2" cx="12" cy="12" r="12"/>
                <path fill="white" d="M18.335 18.339H15.67v-4.177c0-.996-.02-2.278-1.39-2.278-1.389 0-1.601 1.084-1.601 2.205v4.25h-2.666V9.75h2.56v1.17h.035c.358-.674 1.228-1.387 2.528-1.387 2.7 0 3.2 1.778 3.2 4.091v4.715zM7.003 8.575a1.546 1.546 0 0 1-1.548-1.549 1.548 1.548 0 1 1 1.547 1.549zm1.336 9.764H5.666V9.75H8.34v8.589zM19.67 3H4.329C3.593 3 3 3.58 3 4.297v15.406C3 20.42 3.594 21 4.328 21h15.338C20.4 21 21 20.42 21 19.703V4.297C21 3.58 20.4 3 19.666 3h.003z"/>
            </g>

            <!-- Threads Icon -->
            <g id="threads">
                <circle fill="#000000" cx="12" cy="12" r="12"/>
                <path fill="white" d="M12.186 19h-.007c-2.581-.024-4.334-.905-5.184-2.509C5.85 14.94 5.5 13.086 5.5 12.05c0-1.025.35-2.368 1.495-3.509C8.345 6.905 10.098 6.024 12.179 6h.014c2.581.024 4.334.905 5.184 2.509C18.515 10.082 18.5 11.525 18.5 12.05c0 1.535.015 2.978-.623 4.441C16.927 18.095 15.174 18.976 12.193 19h-.007zM12.186 8.5c-1.829.02-2.806.646-3.106 1.704C8.756 10.991 8.5 11.459 8.5 12.05s.256 1.059.58 1.846c.3 1.058 1.277 1.684 3.106 1.704 1.829-.02 2.806-.646 3.106-1.704C15.616 13.109 15.5 12.641 15.5 12.05s.116-1.059-.208-1.846C14.992 9.146 14.015 8.52 12.186 8.5z"/>
                <path fill="white" d="M14.11 11.98c.04-.2.04-.4.02-.6-.12-.28-.24-.51-.46-.68-.22-.17-.52-.27-.82-.33-.3-.06-.62-.06-.92 0-.3.06-.54.16-.82.33-.22.17-.34.4-.46.68-.12.28-.12.52.02.6.04.2.16.4.38.52.22.12.52.17.82.17s.6-.05.82-.17c.22-.12.34-.32.38-.52z"/>
            </g>
        </defs>
    </svg>

    <section class="social-slideshow-section">
        <div class="container-fluid">
            <div class="section-content">
                <!-- Title -->
                <h1 class="main-title">
                    Streamline Your Social Media<br>
                    With Flowkar
                </h1>

                <!-- Subtitle -->
                <p class="subtitle">
                    One tool to schedule, track, and grow your socials.
                </p>

                <!-- Icons Container -->
                <div class="icons-container">
                    <!-- Background Images -->
                            <div class="background-images">
                                <img class="background-shadow" src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Background_Shadow-shUizQnGcNhErNuK4Cd5UTBqjG9ZzP.svg" alt="Shadow" class="bg-shadow" loading="lazy">
                                <img class="background-overlay" src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Background_03-hxju9NR17R6bvWAmAYgIpBAqBcFffx.svg" alt="" class="bg-gradient" loading="lazy">
                            </div>

                    <!-- Social Media Icons Slideshow -->
                    <div class="icons-slideshow">
                        <div class="icons-track" id="iconsTrack">
                            <!-- Icons will be dynamically generated here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

<script>
(function() {
    // Ensure this script only runs once for this component
    if (window.socialIconsInitialized) {
        return;
    }
    window.socialIconsInitialized = true;

    // Social media icons data
    const baseSocialIcons = [
        { id: 'youtube', alt: 'YouTube', platform: 'youtube', color: '#FF0000' },
        { id: 'reddit', alt: 'Reddit', platform: 'reddit', color: '#FF4500' },
        { id: 'instagram', alt: 'Instagram', platform: 'instagram', color: '#E4405F' },
        { id: 'facebook', alt: 'Facebook', platform: 'facebook', color: '#1877F2' },
        { id: 'twitter', alt: 'X (Twitter)', platform: 'twitter', color: '#000000' },
        { id: 'pinterest', alt: 'Pinterest', platform: 'pinterest', color: '#BD081C' },
        { id: 'linkedin', alt: 'LinkedIn', platform: 'linkedin', color: '#0A66C2' },
        { id: 'threads', alt: 'Threads', platform: 'threads', color: '#000000' },
        { id: 'reddit', alt: 'Reddit', platform: 'reddit', color: '#FF4500' },
        { id: 'youtube', alt: 'YouTube', platform: 'youtube', color: '#FF0000' },
        { id: 'facebook', alt: 'Facebook', platform: 'facebook', color: '#1877F2' },
        { id: 'instagram', alt: 'Instagram', platform: 'instagram', color: '#E4405F' },
        { id: 'threads', alt: 'Threads', platform: 'threads', color: '#000000' },
        { id: 'pinterest', alt: 'Pinterest', platform: 'pinterest', color: '#BD081C' },
        { id: 'twitter', alt: 'X (Twitter)', platform: 'twitter', color: '#000000' },
        { id: 'linkedin', alt: 'LinkedIn', platform: 'linkedin', color: '#0A66C2' },
        { id: 'instagram', alt: 'Instagram', platform: 'instagram', color: '#E4405F' },
        { id: 'reddit', alt: 'Reddit', platform: 'reddit', color: '#FF4500' },
        { id: 'pinterest', alt: 'Pinterest', platform: 'pinterest', color: '#BD081C' },
        { id: 'youtube', alt: 'YouTube', platform: 'youtube', color: '#FF0000' },
        { id: 'linkedin', alt: 'LinkedIn', platform: 'linkedin', color: '#0A66C2' },
        { id: 'twitter', alt: 'X (Twitter)', platform: 'twitter', color: '#000000' },
        { id: 'facebook', alt: 'Facebook', platform: 'facebook', color: '#1877F2' },
        { id: 'threads', alt: 'Threads', platform: 'threads', color: '#000000' }
    ];

    // Animation state
    let animationOffset = 0;
    let roundCount = 0;
    let isRestarting = false;
    let spacing = 130;
    let isMobile = false;

    // Generate repeated icons
    function generateSocialIcons(repeatCount) {
        const result = [];
        for (let i = 0; i < repeatCount; i++) {
            result.push(...baseSocialIcons);
        }
        return result;
    }

    const socialIcons = generateSocialIcons(15);
    const centerPosition = Math.floor(socialIcons.length / 2);

    // Responsive handling
    function handleResize() {
        const mobile = window.innerWidth <= 640;
        const newSpacing = mobile ? 170 : window.innerWidth <= 768 ? 100 : 130;
        spacing = newSpacing;
        isMobile = mobile;

        // Update slideshow height - scope to this component
        const slideshow = document.querySelector('#socialIcons .icons-slideshow');
        if (slideshow) {
            slideshow.style.height = mobile ? '120px' : '150px';
        }
    }

    // Calculate icon position
    function getIconPosition(index) {
        const basePosition = (index - centerPosition) * spacing;
        return basePosition - animationOffset;
    }

    // Calculate icon scale
    function getIconScale(index) {
        const effectiveCenterIndex = centerPosition + animationOffset / spacing;
        const cycleLength = baseSocialIcons.length;
        const iconCyclePosition = index % cycleLength;
        const centerCyclePosition = effectiveCenterIndex % cycleLength;

        let distance = Math.abs(iconCyclePosition - centerCyclePosition);
        distance = Math.min(distance, cycleLength - distance);

        if (distance < 0.5) return 1; // Center icon
        if (distance < 1.5) return 0.8; // Adjacent icons
        if (distance < 2.5) return 0.7; // Second level
        return 0.6; // Far icons
    }

    // Calculate icon opacity
    function getIconOpacity(index) {
        const effectiveCenterIndex = centerPosition + animationOffset / spacing;
        const cycleLength = baseSocialIcons.length;
        const iconCyclePosition = index % cycleLength;
        const centerCyclePosition = effectiveCenterIndex % cycleLength;

        let distance = Math.abs(iconCyclePosition - centerCyclePosition);
        distance = Math.min(distance, cycleLength - distance);

        if (distance < 0.5) return 1; // Center icon
        if (distance <= 2.5) return 0.8; // Close icons
        if (distance <= 3.5) return 0.8; // Medium distance
        return 0.4; // Far icons
    }

    // Check if icon is highlighted
    function isIconHighlighted(index) {
        const effectiveCenterIndex = centerPosition + animationOffset / spacing;
        const cycleLength = baseSocialIcons.length;
        const iconCyclePosition = index % cycleLength;
        const centerCyclePosition = effectiveCenterIndex % cycleLength;

        let distance = Math.abs(iconCyclePosition - centerCyclePosition);
        distance = Math.min(distance, cycleLength - distance);

        return distance < 0.5;
    }

    // Create icon element
    function createIconElement(social, index) {
        const iconDiv = document.createElement('div');
        iconDiv.className = 'social-icon';
        iconDiv.setAttribute('data-index', index);

        // Corner brackets
        const cornerTL = document.createElement('span');
        cornerTL.className = 'corner-bracket corner-tl';

        const cornerTR = document.createElement('span');
        cornerTR.className = 'corner-bracket corner-tr';

        const cornerBL = document.createElement('span');
        cornerBL.className = 'corner-bracket corner-bl';

        const cornerBR = document.createElement('span');
        cornerBR.className = 'corner-bracket corner-br';

        // Icon container
        const container = document.createElement('div');
        container.className = 'icon-container';

        // SVG icon
        const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        svg.setAttribute('class', 'social-svg');
        svg.setAttribute('viewBox', '0 0 24 24');

        const use = document.createElementNS('http://www.w3.org/2000/svg', 'use');
        use.setAttributeNS('http://www.w3.org/1999/xlink', 'href', '#' + social.id);

        svg.appendChild(use);
        container.appendChild(svg);

        // Assemble
        iconDiv.appendChild(cornerTL);
        iconDiv.appendChild(cornerTR);
        iconDiv.appendChild(cornerBL);
        iconDiv.appendChild(cornerBR);
        iconDiv.appendChild(container);

        return iconDiv;
    }

        // Calculate icon position
        function getIconPosition(index) {
            const basePosition = (index - centerPosition) * spacing;
            return basePosition - animationOffset;
        }

        // Calculate icon scale
        function getIconScale(index) {
            const effectiveCenterIndex = centerPosition + animationOffset / spacing;
            const cycleLength = baseSocialIcons.length;
            const iconCyclePosition = index % cycleLength;
            const centerCyclePosition = effectiveCenterIndex % cycleLength;

            let distance = Math.abs(iconCyclePosition - centerCyclePosition);
            distance = Math.min(distance, cycleLength - distance);

            if (distance < 0.5) return 1; // Center icon
            if (distance < 1.5) return 0.8; // Adjacent icons
            if (distance < 2.5) return 0.7; // Second level
            return 0.6; // Far icons
        }

        // Calculate icon opacity
        function getIconOpacity(index) {
            const effectiveCenterIndex = centerPosition + animationOffset / spacing;
            const cycleLength = baseSocialIcons.length;
            const iconCyclePosition = index % cycleLength;
            const centerCyclePosition = effectiveCenterIndex % cycleLength;

            let distance = Math.abs(iconCyclePosition - centerCyclePosition);
            distance = Math.min(distance, cycleLength - distance);

            if (distance < 0.5) return 1; // Center icon
            if (distance <= 2.5) return 0.8; // Close icons
            if (distance <= 3.5) return 0.8; // Medium distance
            return 0.4; // Far icons
        }

        // Check if icon is highlighted
        function isIconHighlighted(index) {
            const effectiveCenterIndex = centerPosition + animationOffset / spacing;
            const cycleLength = baseSocialIcons.length;
            const iconCyclePosition = index % cycleLength;
            const centerCyclePosition = effectiveCenterIndex % cycleLength;

            let distance = Math.abs(iconCyclePosition - centerCyclePosition);
            distance = Math.min(distance, cycleLength - distance);
            
            return distance < 0.5;
        }

        // Create icon element
        function createIconElement(social, index) {
            const iconDiv = document.createElement('div');
            iconDiv.className = 'social-icon';
            iconDiv.setAttribute('data-index', index);

            // Corner brackets
            const cornerTL = document.createElement('span');
            cornerTL.className = 'corner-bracket corner-tl';
            
            const cornerTR = document.createElement('span');
            cornerTR.className = 'corner-bracket corner-tr';
            
            const cornerBL = document.createElement('span');
            cornerBL.className = 'corner-bracket corner-bl';
            
            const cornerBR = document.createElement('span');
            cornerBR.className = 'corner-bracket corner-br';

            // Icon container
            const container = document.createElement('div');
            container.className = 'icon-container';

            // SVG icon
            const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
            svg.setAttribute('class', 'social-svg');
            svg.setAttribute('viewBox', '0 0 24 24');
            
            const use = document.createElementNS('http://www.w3.org/2000/svg', 'use');
            use.setAttributeNS('http://www.w3.org/1999/xlink', 'href', '#' + social.id);
            
            svg.appendChild(use);
            container.appendChild(svg);

            // Assemble
            iconDiv.appendChild(cornerTL);
            iconDiv.appendChild(cornerTR);
            iconDiv.appendChild(cornerBL);
            iconDiv.appendChild(cornerBR);
            iconDiv.appendChild(container);

            return iconDiv;
        }

    // Update icon positions and styles
    function updateIcons() {
        const iconsTrack = document.getElementById('iconsTrack');
        if (!iconsTrack) return;

        const icons = iconsTrack.querySelectorAll('.social-icon');

        icons.forEach((icon, index) => {
            const social = socialIcons[index];
            const scale = getIconScale(index);
            const opacity = getIconOpacity(index);
            const translateX = getIconPosition(index);
            const highlighted = isIconHighlighted(index);

            // Update position and scale
            if (isMobile) {
                icon.style.transform = `translateX(${translateX}px)`;
            } else {
                icon.style.transform = `translateX(${translateX}px) scale(${scale})`;
            }

            icon.style.opacity = opacity;
            icon.style.zIndex = highlighted ? 20 : 10 - Math.floor(Math.abs(translateX) / spacing);

            // Update container
            const container = icon.querySelector('.icon-container');
            if (highlighted) {
                container.classList.add('highlighted');
                container.style.backgroundColor = `${social.color}10`;
            } else {
                container.classList.remove('highlighted');
                container.style.backgroundColor = '#f8f9fa';
            }

            // Update container size with !important to override global styles
            const size = highlighted ? (isMobile ? '72px' : '96px') : (isMobile ? '56px' : '62px');
            container.style.setProperty('width', size, 'important');
            container.style.setProperty('height', size, 'important');
            container.style.setProperty('min-width', size, 'important');
            container.style.setProperty('min-height', size, 'important');
            container.style.setProperty('max-width', size, 'important');
            container.style.setProperty('max-height', size, 'important');
            container.style.setProperty('border-radius', '9px', 'important');

            // Update SVG size
            const svg = container.querySelector('.social-svg');
            const iconSize = highlighted ? (isMobile ? '48px' : '64px') : (isMobile ? '32px' : '40px');
            svg.style.width = iconSize;
            svg.style.height = iconSize;

            // Update corner brackets
            const brackets = icon.querySelectorAll('.corner-bracket');
            brackets.forEach(bracket => {
                bracket.style.borderColor = highlighted ? social.color : 'var(--border-inactive)';
            });
        });
    }

    // Initialize icons
    function initializeIcons() {
        const iconsTrack = document.getElementById('iconsTrack');
        if (!iconsTrack) return;

        iconsTrack.innerHTML = '';

        socialIcons.forEach((social, index) => {
            const iconElement = createIconElement(social, index);
            iconsTrack.appendChild(iconElement);
        });

        updateIcons();
    }

    // Animation loop
    function animate() {
        animationOffset += spacing;

        const cycleLength = baseSocialIcons.length * spacing;

        if (animationOffset >= cycleLength) {
            roundCount++;

            if (roundCount >= 15) {
                // Restart animation
                isRestarting = true;
                const iconsTrack = document.getElementById('iconsTrack');
                if (iconsTrack) {
                    iconsTrack.classList.add('restarting');

                    setTimeout(() => {
                        animationOffset = 0;
                        roundCount = 0;
                        isRestarting = false;
                        iconsTrack.classList.remove('restarting');
                        updateIcons();
                    }, 1000);
                }

                return;
            }

            animationOffset = 0;
        }

        updateIcons();
    }

    // Initialize when component is loaded
    function initComponent() {
        handleResize();
        initializeIcons();

        // Start animation
        setInterval(animate, 2000);

        // Handle window resize
        window.addEventListener('resize', () => {
            handleResize();
            updateIcons();
        });
    }

    // Wait for DOM to be ready, then initialize
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initComponent);
    } else {
        // DOM is already loaded, initialize immediately
        setTimeout(initComponent, 100); // Small delay to ensure component is inserted
    }

})(); // End of IIFE
</script>