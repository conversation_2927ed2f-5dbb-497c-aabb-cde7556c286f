<!-- Social Media Icons Component Styles -->
<style>
    /* Scoped styles for social media icons component */
    #socialIcons {
        --primary-text: #563D39;
        --secondary-text: #00000099;
        --bg-white: #FFFFFF;
        --youtube-color: #FF0000;
        --reddit-color: #FF4500;
        --instagram-color: #E4405F;
        --facebook-color: #1877F2;
        --twitter-color: #000000;
        --pinterest-color: #BD081C;
        --linkedin-color: #0A66C2;
        --threads-color: #000000;
        --border-inactive: #d1d5db;
        --shadow-light: rgba(0, 0, 0, 0.1);
        --shadow-medium: rgba(0, 0, 0, 0.04);
    }

    #socialIcons .social-slideshow-section {
        width: 100%;
        padding: 144px 16px;
        background-color: var(--bg-white);
        position: relative;
        overflow: hidden;
        display: flex;
        align-items: center;
        min-height: 100vh;
        font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON>', <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, sans-serif;
        line-height: 1.6;
    }

    #socialIcons .section-content {
        text-align: center;
        position: relative;
        z-index: 10;
        width: 100%;
        margin-top: -20px;
    }

    #socialIcons .main-title {
        font-size: 43px;
        font-weight: 500;
        color: var(--primary-text);
        margin-bottom: 24px;
        line-height: 1.2;
    }

    #socialIcons .subtitle {
        min-height: 10vh;
        font-size: 24px;
        color: var(--secondary-text);
        max-width: 768px;
        margin: 0 auto 78px auto;
        font-weight: 300;
    }

    #socialIcons .icons-container {
        position: relative;
    }

    #socialIcons .background-images {
        position: absolute;
        inset: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        pointer-events: none;
    }

    #socialIcons .background-shadow,
    #socialIcons .background-overlay {
        position: absolute;
        width: 583px;
        height: 500px;
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
    }

    #socialIcons .icons-slideshow {
        position: relative;
        overflow: hidden;
        height: 150px;
    }

    #socialIcons .icons-track {
        position: absolute;
        inset: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: opacity 1s ease-in-out;
    }

    #socialIcons .icons-track.restarting {
        opacity: 0.3;
    }

    #socialIcons .social-icon {
        position: absolute;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 9px;
        transition: all 1s ease-in-out;
    }

    #socialIcons .social-icon .icon-container {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 9px !important;
        transition: all 1s ease-in-out;
        background-color: #f8f9fa;
        box-shadow: 0 10px 15px -3px var(--shadow-light), 0 4px 6px -2px var(--shadow-medium);
        flex-shrink: 0;
        width: auto !important;
        height: auto !important;
    }

    #socialIcons .social-icon .icon-container.highlighted {
        box-shadow: 0 20px 25px -5px var(--shadow-light), 0 10px 10px -5px var(--shadow-medium);
    }

    #socialIcons .social-icon img {
        object-fit: contain;
        position: relative;
        z-index: 10;
        transition: all 1s ease-in-out;
        flex-shrink: 0;
        width: 100%;
        height: 100%;
        border-radius: 4px;
    }

    /* Corner brackets */
    #socialIcons .corner-bracket {
        position: absolute;
        width: 24px;
        height: 24px;
        z-index: 20;
        transition: border-color 1s ease-in-out;
        border-color: var(--border-inactive);
    }

    #socialIcons .corner-tl {
        top: -12px;
        left: -12px;
        border-top: 2px solid;
        border-left: 2px solid;
        border-top-left-radius: 10px;
    }

    #socialIcons .corner-tr {
        top: -12px;
        right: -12px;
        border-top: 2px solid;
        border-right: 2px solid;
        border-top-right-radius: 10px;
    }

    #socialIcons .corner-bl {
        bottom: -12px;
        left: -12px;
        border-bottom: 2px solid;
        border-left: 2px solid;
        border-bottom-left-radius: 10px;
    }

    #socialIcons .corner-br {
        bottom: -12px;
        right: -12px;
        border-bottom: 2px solid;
        border-right: 2px solid;
        border-bottom-right-radius: 10px;
    }

    /* Social media icon images */
    #socialIcons .social-svg {
        display: inline-block;
        width: 100%;
        height: 100%;
        object-fit: contain;
        border-radius: 4px;
    }

    /* Responsive styles */
    @media (max-width: 1200px) {
        #socialIcons .main-title {
            font-size: 38px;
        }
        #socialIcons .subtitle {
            font-size: 20px;
        }
    }

    @media (max-width: 768px) {
        #socialIcons .main-title {
            font-size: 30px;
        }
        #socialIcons .subtitle {
            font-size: 16px;
        }
        #socialIcons .icons-slideshow {
            height: 120px;
        }
        #socialIcons .corner-bracket {
            left: -24px !important;
            right: -24px !important;
        }
        #socialIcons .corner-tl, #socialIcons .corner-bl {
            left: -24px;
        }
        #socialIcons .corner-tr, #socialIcons .corner-br {
            right: -24px;
        }
    }

    @media (max-width: 640px) {
        #socialIcons .social-slideshow-section {
            padding: 144px 16px;
        }
    }
</style>


    <section class="social-slideshow-section">
        <div class="container-fluid">
            <div class="section-content">
                <!-- Title -->
                <h1 class="main-title">
                    Streamline Your Social Media<br>
                    With Flowkar
                </h1>

                <!-- Subtitle -->
                <p class="subtitle">
                    One tool to schedule, track, and grow your socials.
                </p>

                <!-- Icons Container -->
                <div class="icons-container">
                    <!-- Background Images -->
                            <div class="background-images">
                                <img class="background-shadow" src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Background_Shadow-shUizQnGcNhErNuK4Cd5UTBqjG9ZzP.svg" alt="Shadow" class="bg-shadow" loading="lazy">
                                <img class="background-overlay" src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Background_03-hxju9NR17R6bvWAmAYgIpBAqBcFffx.svg" alt="" class="bg-gradient" loading="lazy">
                            </div>

                    <!-- Social Media Icons Slideshow -->
                    <div class="icons-slideshow">
                        <div class="icons-track" id="iconsTrack">
                            <!-- Icons will be dynamically generated here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

<script>
(function() {
    // Ensure this script only runs once for this component
    if (window.socialIconsInitialized) {
        return;
    }
    window.socialIconsInitialized = true;

    // Social media icons data with custom SVG paths
    const baseSocialIcons = [
        { id: 'youtube', alt: 'YouTube', platform: 'youtube', color: '#FF0000', icon: '../assets/svg_icon/youtube-icon.svg' },
        { id: 'reddit', alt: 'Reddit', platform: 'reddit', color: '#FF4500', icon: '../assets/svg_icon/reddit_icon.svg' },
        { id: 'instagram', alt: 'Instagram', platform: 'instagram', color: '#E4405F', icon: '../assets/svg_icon/instagram.svg' },
        { id: 'facebook', alt: 'Facebook', platform: 'facebook', color: '#1877F2', icon: '../assets/svg_icon/facebook-icon.svg' },
        { id: 'twitter', alt: 'X (Twitter)', platform: 'twitter', color: '#000000', icon: '../assets/svg_icon/twitter-icon.svg' },
        { id: 'pinterest', alt: 'Pinterest', platform: 'pinterest', color: '#BD081C', icon: '../assets/svg_icon/pintrest-icon.svg' },
        { id: 'linkedin', alt: 'LinkedIn', platform: 'linkedin', color: '#0A66C2', icon: '../assets/svg_icon/linkdin-icon.svg' },
        { id: 'threads', alt: 'Threads', platform: 'threads', color: '#000000', icon: '../assets/svg_icon/thread.svg' },
        { id: 'reddit', alt: 'Reddit', platform: 'reddit', color: '#FF4500', icon: '../assets/svg_icon/reddit_icon.svg' },
        { id: 'youtube', alt: 'YouTube', platform: 'youtube', color: '#FF0000', icon: '../assets/svg_icon/youtube-icon.svg' },
        { id: 'facebook', alt: 'Facebook', platform: 'facebook', color: '#1877F2', icon: '../assets/svg_icon/facebook-icon.svg' },
        { id: 'instagram', alt: 'Instagram', platform: 'instagram', color: '#E4405F', icon: '../assets/svg_icon/instagram.svg' },
        { id: 'threads', alt: 'Threads', platform: 'threads', color: '#000000', icon: '../assets/svg_icon/thread.svg' },
        { id: 'pinterest', alt: 'Pinterest', platform: 'pinterest', color: '#BD081C', icon: '../assets/svg_icon/pintrest-icon.svg' },
        { id: 'twitter', alt: 'X (Twitter)', platform: 'twitter', color: '#000000', icon: '../assets/svg_icon/twitter-icon.svg' },
        { id: 'linkedin', alt: 'LinkedIn', platform: 'linkedin', color: '#0A66C2', icon: '../assets/svg_icon/linkdin-icon.svg' },
        { id: 'instagram', alt: 'Instagram', platform: 'instagram', color: '#E4405F', icon: '../assets/svg_icon/instagram.svg' },
        { id: 'reddit', alt: 'Reddit', platform: 'reddit', color: '#FF4500', icon: '../assets/svg_icon/reddit_icon.svg' },
        { id: 'pinterest', alt: 'Pinterest', platform: 'pinterest', color: '#BD081C', icon: '../assets/svg_icon/pintrest-icon.svg' },
        { id: 'youtube', alt: 'YouTube', platform: 'youtube', color: '#FF0000', icon: '../assets/svg_icon/youtube-icon.svg' },
        { id: 'linkedin', alt: 'LinkedIn', platform: 'linkedin', color: '#0A66C2', icon: '../assets/svg_icon/linkdin-icon.svg' },
        { id: 'twitter', alt: 'X (Twitter)', platform: 'twitter', color: '#000000', icon: '../assets/svg_icon/twitter-icon.svg' },
        { id: 'facebook', alt: 'Facebook', platform: 'facebook', color: '#1877F2', icon: '../assets/svg_icon/facebook-icon.svg' },
        { id: 'threads', alt: 'Threads', platform: 'threads', color: '#000000', icon: '../assets/svg_icon/thread.svg' }
    ];

    // Animation state
    let animationOffset = 0;
    let roundCount = 0;
    let isRestarting = false;
    let spacing = 130;
    let isMobile = false;

    // Generate repeated icons
    function generateSocialIcons(repeatCount) {
        const result = [];
        for (let i = 0; i < repeatCount; i++) {
            result.push(...baseSocialIcons);
        }
        return result;
    }

    const socialIcons = generateSocialIcons(15);
    const centerPosition = Math.floor(socialIcons.length / 2);

    // Responsive handling
    function handleResize() {
        const mobile = window.innerWidth <= 640;
        const newSpacing = mobile ? 170 : window.innerWidth <= 768 ? 100 : 130;
        spacing = newSpacing;
        isMobile = mobile;

        // Update slideshow height - scope to this component
        const slideshow = document.querySelector('#socialIcons .icons-slideshow');
        if (slideshow) {
            slideshow.style.height = mobile ? '120px' : '150px';
        }
    }

    // Calculate icon position
    function getIconPosition(index) {
        const basePosition = (index - centerPosition) * spacing;
        return basePosition - animationOffset;
    }

    // Calculate icon scale
    function getIconScale(index) {
        const effectiveCenterIndex = centerPosition + animationOffset / spacing;
        const cycleLength = baseSocialIcons.length;
        const iconCyclePosition = index % cycleLength;
        const centerCyclePosition = effectiveCenterIndex % cycleLength;

        let distance = Math.abs(iconCyclePosition - centerCyclePosition);
        distance = Math.min(distance, cycleLength - distance);

        if (distance < 0.5) return 1; // Center icon
        if (distance < 1.5) return 0.8; // Adjacent icons
        if (distance < 2.5) return 0.7; // Second level
        return 0.6; // Far icons
    }

    // Calculate icon opacity
    function getIconOpacity(index) {
        const effectiveCenterIndex = centerPosition + animationOffset / spacing;
        const cycleLength = baseSocialIcons.length;
        const iconCyclePosition = index % cycleLength;
        const centerCyclePosition = effectiveCenterIndex % cycleLength;

        let distance = Math.abs(iconCyclePosition - centerCyclePosition);
        distance = Math.min(distance, cycleLength - distance);

        if (distance < 0.5) return 1; // Center icon
        if (distance <= 2.5) return 0.8; // Close icons
        if (distance <= 3.5) return 0.8; // Medium distance
        return 0.4; // Far icons
    }

    // Check if icon is highlighted
    function isIconHighlighted(index) {
        const effectiveCenterIndex = centerPosition + animationOffset / spacing;
        const cycleLength = baseSocialIcons.length;
        const iconCyclePosition = index % cycleLength;
        const centerCyclePosition = effectiveCenterIndex % cycleLength;

        let distance = Math.abs(iconCyclePosition - centerCyclePosition);
        distance = Math.min(distance, cycleLength - distance);

        return distance < 0.5;
    }

    // Create icon element
    function createIconElement(social, index) {
        const iconDiv = document.createElement('div');
        iconDiv.className = 'social-icon';
        iconDiv.setAttribute('data-index', index);

        // Corner brackets
        const cornerTL = document.createElement('span');
        cornerTL.className = 'corner-bracket corner-tl';

        const cornerTR = document.createElement('span');
        cornerTR.className = 'corner-bracket corner-tr';

        const cornerBL = document.createElement('span');
        cornerBL.className = 'corner-bracket corner-bl';

        const cornerBR = document.createElement('span');
        cornerBR.className = 'corner-bracket corner-br';

        // Icon container
        const container = document.createElement('div');
        container.className = 'icon-container';

        // Custom SVG icon
        const img = document.createElement('img');
        img.setAttribute('class', 'social-svg');
        img.setAttribute('src', social.icon);
        img.setAttribute('alt', social.alt);
        img.setAttribute('loading', 'lazy');

        container.appendChild(img);

        // Assemble
        iconDiv.appendChild(cornerTL);
        iconDiv.appendChild(cornerTR);
        iconDiv.appendChild(cornerBL);
        iconDiv.appendChild(cornerBR);
        iconDiv.appendChild(container);

        return iconDiv;
    }



    // Update icon positions and styles
    function updateIcons() {
        const iconsTrack = document.getElementById('iconsTrack');
        if (!iconsTrack) return;

        const icons = iconsTrack.querySelectorAll('.social-icon');

        icons.forEach((icon, index) => {
            const social = socialIcons[index];
            const scale = getIconScale(index);
            const opacity = getIconOpacity(index);
            const translateX = getIconPosition(index);
            const highlighted = isIconHighlighted(index);

            // Update position and scale
            if (isMobile) {
                icon.style.transform = `translateX(${translateX}px)`;
            } else {
                icon.style.transform = `translateX(${translateX}px) scale(${scale})`;
            }

            icon.style.opacity = opacity;
            icon.style.zIndex = highlighted ? 20 : 10 - Math.floor(Math.abs(translateX) / spacing);

            // Update container
            const container = icon.querySelector('.icon-container');
            if (highlighted) {
                container.classList.add('highlighted');
                container.style.backgroundColor = `${social.color}10`;
            } else {
                container.classList.remove('highlighted');
                container.style.backgroundColor = '#f8f9fa';
            }

            // Update container size with !important to override global styles
            const size = highlighted ? (isMobile ? '72px' : '96px') : (isMobile ? '56px' : '62px');
            container.style.setProperty('width', size, 'important');
            container.style.setProperty('height', size, 'important');
            container.style.setProperty('min-width', size, 'important');
            container.style.setProperty('min-height', size, 'important');
            container.style.setProperty('max-width', size, 'important');
            container.style.setProperty('max-height', size, 'important');
            container.style.setProperty('border-radius', '9px', 'important');

            // Update icon size
            const iconImg = container.querySelector('.social-svg');
            const iconSize = highlighted ? (isMobile ? '48px' : '64px') : (isMobile ? '32px' : '40px');
            iconImg.style.width = iconSize;
            iconImg.style.height = iconSize;

            // Update corner brackets
            const brackets = icon.querySelectorAll('.corner-bracket');
            brackets.forEach(bracket => {
                bracket.style.borderColor = highlighted ? social.color : 'var(--border-inactive)';
            });
        });
    }

    // Initialize icons
    function initializeIcons() {
        const iconsTrack = document.getElementById('iconsTrack');
        if (!iconsTrack) return;

        iconsTrack.innerHTML = '';

        socialIcons.forEach((social, index) => {
            const iconElement = createIconElement(social, index);
            iconsTrack.appendChild(iconElement);
        });

        updateIcons();
    }

    // Animation loop
    function animate() {
        animationOffset += spacing;

        const cycleLength = baseSocialIcons.length * spacing;

        if (animationOffset >= cycleLength) {
            roundCount++;

            if (roundCount >= 15) {
                // Restart animation
                isRestarting = true;
                const iconsTrack = document.getElementById('iconsTrack');
                if (iconsTrack) {
                    iconsTrack.classList.add('restarting');

                    setTimeout(() => {
                        animationOffset = 0;
                        roundCount = 0;
                        isRestarting = false;
                        iconsTrack.classList.remove('restarting');
                        updateIcons();
                    }, 1000);
                }

                return;
            }

            animationOffset = 0;
        }

        updateIcons();
    }

    // Initialize when component is loaded
    function initComponent() {
        handleResize();
        initializeIcons();

        // Start animation
        setInterval(animate, 2000);

        // Handle window resize
        window.addEventListener('resize', () => {
            handleResize();
            updateIcons();
        });
    }

    // Wait for DOM to be ready, then initialize
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initComponent);
    } else {
        // DOM is already loaded, initialize immediately
        setTimeout(initComponent, 100); // Small delay to ensure component is inserted
    }

})(); // End of IIFE
</script>