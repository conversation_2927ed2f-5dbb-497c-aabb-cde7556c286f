:root {
    --primary: #563D39;
    --secondary: #5a3a32;
    --accent: #8B4513;
    --light: #F8F4F0;
    --dark: #2A211F;
    --gray: #6D6D6D;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--light);
    color: var(--dark);
    padding-top: 80px;
}

.navbar {
    background-color: white;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
    padding: 15px 0;
}

.hero-banner {
    background: linear-gradient(rgba(86, 61, 57, 0.9), rgba(86, 61, 57, 0.8)), url('https://images.unsplash.com/photo-1499750310107-5fef28a66643?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80');
    background-size: cover;
    background-position: center;
    border-radius: 20px;
    padding: 80px 40px;
    margin: 40px auto;
    color: white;
    text-align: center;
}

.section-title {
    position: relative;
    margin-bottom: 40px;
    color: var(--primary);
}

.section-title:after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 60px;
    height: 3px;
    background-color: var(--accent);
}

.blog-card {
    transition: all 0.3s ease;
    border-radius: 16px;
    overflow: hidden;
    height: 100%;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.07);
    border: 1px solid rgba(0, 0, 0, 0.05);
    background: white;
}

.blog-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.15);
}

.blog-card-img {
    height: 200px;
    width: 100%;
    object-fit: cover;
}

.blog-card-body {
    padding: 20px;
}

.blog-card-title {
    font-weight: 700;
    margin-bottom: 12px;
    color: var(--dark);
    font-size: 1.1rem;
    min-height: 52px;
}

.blog-card-meta {
    display: flex;
    justify-content: space-between;
    color: var(--gray);
    font-size: 0.85rem;
}

.featured-blog {
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 40px;
    background: white;
}

.featured-img {
    height: 300px;
    object-fit: cover;
}

.featured-content {
    padding: 30px;
}

.featured-title {
    font-weight: 800;
    font-size: 2rem;
    margin-bottom: 15px;
    color: var(--primary);
}

.sidebar {
    background: white;
    border-radius: 16px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.07);
    margin-bottom: 30px;
}

.search-box {
    background: white;
    border-radius: 100px;
    padding: 12px 20px;
    border: 1px solid rgba(86, 61, 57, 0.5);
    display: flex;
    align-items: center;
}

.search-box input {
    border: none;
    outline: none;
    flex: 1;
    padding-left: 10px;
    background: transparent;
}

.recent-post {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.recent-post:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.recent-img {
    width: 100px;
    height: 100px;
    border-radius: 12px;
    object-fit: cover;
}

.recent-title {
    font-weight: 700;
    font-size: 0.95rem;
    margin-bottom: 5px;
    color: var(--dark);
}

.recent-meta {
    color: var(--gray);
    font-size: 0.8rem;
}

.explore-section {
    background-color: var(--primary);
    color: white;
    border-radius: 32px;
    padding: 60px 40px;
    margin: 60px 0;
}

.explore-card {
    transition: all 0.3s ease;
    height: 100%;
}

.explore-card:hover {
    transform: translateY(-5px);
}

.explore-img {
    height: 200px;
    width: 100%;
    object-fit: cover;
    border-radius: 16px;
    margin-bottom: 15px;
}

.explore-title {
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 8px;
    min-height: 48px;
}

.explore-meta {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.85rem;
}

.pagination-container {
    position: relative;
    margin-top: 40px;
}

.pagination-border {
    position: absolute;
    top: -30px;
    left: 0;
    right: 0;
    text-align: center;
}

.pagination-buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
}

.page-numbers {
    display: flex;
    gap: 8px;
}

.page-number {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    transition: all 0.3s ease;
}

.page-number.active {
    background: white;
    color: var(--primary);
    font-weight: 600;
}

.page-number:hover:not(.active) {
    background: rgba(255, 255, 255, 0.2);
}

.video-vibes-section {
    padding: 60px 0;
}

.video-card {
    position: relative;
    border-radius: 24px;
    overflow: hidden;
    margin-bottom: 25px;
    height: 400px;
}

.video-card-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.play-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 92px;
    height: 92px;
    border: 1px solid rgba(255, 255, 255, 0.4);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.3);
}

.play-icon-inner {
    width: 76px;
    height: 76px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.24);
    display: flex;
    align-items: center;
    justify-content: center;
}

.play-icon svg {
    width: 40px;
    height: 40px;
    fill: white;
}

.small-video-card {
    position: relative;
    border-radius: 16px;
    overflow: hidden;
    height: 180px;
    margin-bottom: 20px;
}

.small-video-card .play-icon {
    width: 60px;
    height: 60px;
}

.small-video-card .play-icon-inner {
    width: 46px;
    height: 46px;
}

.small-video-card .play-icon svg {
    width: 24px;
    height: 24px;
}

.video-content h3 {
    font-weight: 700;
    font-size: 1.3rem;
    margin-bottom: 10px;
    color: var(--primary);
}

.video-content p {
    color: var(--gray);
    margin-bottom: 15px;
}

.video-meta {
    color: var(--gray);
    font-size: 0.9rem;
}

.footer {
    background: var(--primary);
    padding: 60px 0 30px;
    color: white;
}

.footer-logo {
    height: 40px;
    margin-bottom: 20px;
}

.footer h5 {
    margin-bottom: 20px;
    font-weight: 700;
}

.footer-links a {
    display: block;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    margin-bottom: 10px;
    transition: all 0.3s ease;
}

.footer-links a:hover {
    color: white;
    padding-left: 5px;
}

.social-links {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.social-link {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.social-link:hover {
    transform: translateY(-5px);
    background: var(--accent);
}

.copyright {
    text-align: center;
    padding-top: 30px;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 40px;
}

/* Responsive adjustments */
@media (max-width: 991px) {
    .hero-banner {
        padding: 50px 20px;
    }
    
    .featured-img {
        height: 250px;
    }
    
    .video-card {
        height: 300px;
    }
}

@media (max-width: 767px) {
    .hero-banner {
        padding: 40px 15px;
        margin: 20px auto;
    }
    
    .featured-img {
        height: 200px;
    }
    
    .featured-title {
        font-size: 1.6rem;
    }
    
    .video-card {
        height: 250px;
        margin-bottom: 30px;
    }
    
    .explore-section {
        padding: 40px 20px;
        border-radius: 20px;
    }
}

/* Line clamp utilities */
.line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}