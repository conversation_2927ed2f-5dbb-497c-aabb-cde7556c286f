
/* ---------------------------- Footer  ---------------------------- */

/* Custom CSS for Footer Component */

/* Footer Container */
.footer-container {
    width: 90%;
    margin: 0 auto;
    border-radius: 30px;
    padding: 30px 0;
    opacity: 100%;
    transition: opacity 0.5s ease-in-out;
}

.footer-container.fade-in {
    opacity: 1;
}

/* Main Footer Section */
.footer-main {
    background-color: #563D391A;
    border-top-left-radius: 30px;
    border-top-right-radius: 30px;
}

/* Footer Brand Section */
.footer-brand {
    padding-right: 0;
}

@media (min-width: 768px) {
    .footer-brand {
        padding-right: 2rem;
    }
}

@media (min-width: 992px) {
    .footer-brand {
        padding-right: 3rem;
    }
}

.footer-logo {
    width: 128px;
    height: auto;
}

.footer-description {
    font-size: 14px;
    font-weight: 500;
    color: #00000099;
    line-height: 28px;
    max-width: 250px;
}

@media (min-width: 768px) {
    .footer-description {
        font-size: 16px;
        max-width: 280px;
    }
}

@media (min-width: 992px) {
    .footer-description {
        max-width: 320px;
    }
}

/* Footer Headings */
.footer-heading {
    font-size: 1.125rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 1rem;
}

/* Footer Text */
.footer-text {
    color: #374151;
    font-size: 16px;
    margin-bottom: 0;
}

.footer-email {
    color: #374151;
    font-size: 14px;
    text-decoration: none;
    transition: text-decoration 0.2s ease;
}

.footer-email:hover {
    color: #374151;
    text-decoration: underline;
}

/* Footer Navigation */
.footer-nav {
    margin-bottom: 0;
}

.footer-link {
    color: #374151;
    font-size: 14px;
    text-decoration: none;
    transition: color 0.2s ease, text-decoration 0.2s ease;
}

.footer-link:hover {
    color: #563D39;
    text-decoration: underline;
}

/* Bottom Footer Section */
.footer-bottom {
    background-color: #563D39;
    border-bottom-left-radius: 30px;
    border-bottom-right-radius: 30px;
}

.footer-copyright {
    color: white;
    font-size: 14px;
    margin: 0;
}

.footer-legal {
    gap: 1.5rem;
}

.footer-legal-link {
    color: white;
    font-size: 14px;
    text-decoration: none;
    transition: color 0.2s ease;
}

.footer-legal-link:hover {
    color: #d1d5db;
}

/* Responsive adjustments */
@media (max-width: 575.98px) {
    .footer-legal {
        margin-top: 0.5rem;
    }
}

/* Animation keyframes */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Ensure proper spacing and alignment */
.footer-section {
    height: 100%;
}

.footer-content {
    width: 100%;
}

/* Custom spacing for better visual consistency */
.footer-main .container-fluid {
    padding-left: 2rem;
    padding-right: 2rem;
    padding-top: 3rem;
    padding-bottom: 3rem;
}

.footer-bottom .container-fluid {
    padding-left: 2rem;
    padding-right: 2rem;
}

/* Ensure consistent text alignment on mobile */
@media (max-width: 767.98px) {
    .footer-brand,
    .footer-section {
        text-align: center;
    }
    
    .footer-description {
        margin: 0 auto;
    }
}