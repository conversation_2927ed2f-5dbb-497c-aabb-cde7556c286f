<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>InfoCard - Bootstrap Conversion</title>
    
    <!-- Bootstrap 5.3.x CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/project-folder/css/style.css">
</head>
<body>
    <!-- InfoCard Section -->
    <section class="info-card-section py-5">
        <div class="info-card-container">
            <div class="info-card-wrapper">
                <div class="row g-4 g-lg-5 h-100 align-items-stretch">
                    <!-- Left Side - Main Feature Card -->
                    <div class="col-12 col-lg-6">
                        <div class="main-feature-card">
                            <div class="image-container">
                                <img src="/project-folder/assets/InfoCard/LeftImage.svg" alt="Unified Dashboard" class="main-image">
                                <!-- Blur overlay at bottom -->
                                <div class="image-overlay"></div>
                                <!-- Title positioned on blur -->
                                <div class="image-content">
                                    <h2 class="text-white fw-semibold main-title">
                                        Unified Dashboard For Instagram, Facebook, LinkedIn, YouTube, And More
                                    </h2>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Right Side - Feature List -->
                    <div class="col-12 col-lg-6">
                        <div class="features-container d-flex flex-column justify-content-center h-100">
                            <!-- Feature 1 -->
                            <div class="feature-item d-flex align-items-start">
                                <div class="feature-icon-container flex-shrink-0">
                                    <img src="/project-folder/assets/InfoCard/Icon_01.svg" alt="Real-Time Tracking Icon" class="feature-icon">
                                </div>
                                <div class="feature-content flex-grow-1">
                                    <h3 class="text-white fw-semibold feature-title mb-2">
                                        Real-Time Comment, Like & DM Tracking
                                    </h3>
                                    <p class="feature-description">
                                        Track user engagement in real time to respond instantly - every second counts when capturing attention and driving meaningful interactions.
                                    </p>
                                </div>
                            </div>

                            <!-- Feature 2 -->
                            <div class="feature-item d-flex align-items-start">
                                <div class="feature-icon-container flex-shrink-0">
                                    <img src="/project-folder/assets/InfoCard/Icon_02.svg" alt="Video Insights Icon" class="feature-icon">
                                </div>
                                <div class="feature-content flex-grow-1">
                                    <h3 class="text-white fw-semibold feature-title mb-2">
                                        Reels And Short Video Insights
                                    </h3>
                                    <p class="feature-description">
                                        Monitor views, reach, and engagement for every short video in real time - live insights give you clarity and control instantly.
                                    </p>
                                </div>
                            </div>

                            <!-- Feature 3 -->
                            <div class="feature-item d-flex align-items-start">
                                <div class="feature-icon-container flex-shrink-0">
                                    <img src="/project-folder/assets/InfoCard/Icon_03.svg" alt="Team Collaboration Icon" class="feature-icon">
                                </div>
                                <div class="feature-content flex-grow-1">
                                    <h3 class="text-white fw-semibold feature-title mb-2">
                                        Team Collaboration & Approval Workflows
                                    </h3>
                                    <p class="feature-description">
                                        Seamlessly collaborate with your team - review content, share feedback, and approve projects together in one unified, efficient workflow.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Bootstrap 5.3.x JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
