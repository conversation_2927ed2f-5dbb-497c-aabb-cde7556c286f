 fetch('components/header/header.html')
  .then(res => res.text())
  .then(data => {
    document.getElementById('header').innerHTML = data;
  });



fetch('components/blog/blog.html')
  .then(res => res.text())
  .then(data => {
    document.getElementById('blog').innerHTML = data;
  });

 
fetch('components/footer/footer.html')
  .then(res => res.text())
  .then(data => {
    document.getElementById('footer').innerHTML = data;
  });

  