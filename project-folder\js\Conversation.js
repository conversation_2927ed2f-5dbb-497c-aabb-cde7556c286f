// JavaScript for Conversation Section functionality

document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const skeleton = document.getElementById('skeleton');
    const mainContent = document.getElementById('mainContent');
    const contactForm = document.getElementById('contactForm');
    const submitBtn = document.getElementById('submitBtn');
    const successToast = document.getElementById('successToast');
    const modal = document.getElementById('contactModal');
    
    // Show main content after loading delay
    setTimeout(() => {
        skeleton.classList.add('d-none');
        mainContent.classList.remove('d-none');
        mainContent.classList.add('fade-in');
    }, 1000);
    
    // Form validation and submission
    const formInputs = {
        name: document.getElementById('contactName'),
        email: document.getElementById('contactEmail'),
        description: document.getElementById('contactDescription')
    };
    
    // Clear errors when user starts typing
    Object.keys(formInputs).forEach(key => {
        formInputs[key].addEventListener('input', function() {
            this.classList.remove('is-invalid');
            this.nextElementSibling.textContent = '';
        });
    });
    
    // Form submission
    submitBtn.addEventListener('click', function() {
        handleFormSubmit();
    });
    
    // Reset form when modal is closed
    modal.addEventListener('hidden.bs.modal', function() {
        resetForm();
    });
    
    function handleFormSubmit() {
        // Reset previous errors
        clearErrors();
        
        let hasErrors = false;
        const formData = {};
        
        // Collect form data and validate
        Object.keys(formInputs).forEach(key => {
            const input = formInputs[key];
            const value = input.value.trim();
            formData[key] = value;
            
            if (!value) {
                showError(input, `${key.charAt(0).toUpperCase() + key.slice(1)} is required`);
                hasErrors = true;
            }
        });
        
        // Enhanced email validation
        if (formData.email && !hasErrors) {
            const emailValidation = validateEmail(formData.email);
            if (!emailValidation.isValid) {
                showError(formInputs.email, emailValidation.message);
                hasErrors = true;
            } else if (emailValidation.suggestion) {
                // Show confirmation for typo correction
                const confirmed = confirm(
                    `Did you mean ${emailValidation.suggestion}? Click OK to use the corrected email or Cancel to edit manually.`
                );
                if (confirmed) {
                    formInputs.email.value = emailValidation.suggestion;
                    formData.email = emailValidation.suggestion;
                } else {
                    return;
                }
            }
        }
        
        if (hasErrors) {
            return;
        }
        
        // Start loading state
        setSubmitLoading(true);
        
        // Simulate API call
        const randomDelay = Math.floor(Math.random() * 1000) + 1000; // 1000-2000ms
        
        setTimeout(() => {
            console.log('Form submitted:', formData);
            
            // Show success message
            showSuccessToast();
            
            // Close modal and reset form
            const modalInstance = bootstrap.Modal.getInstance(modal);
            modalInstance.hide();
            
            setSubmitLoading(false);
        }, randomDelay);
    }
    
    function validateEmail(email) {
        const emailLower = email.toLowerCase();
        
        // Basic email format validation
        const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
        if (!emailRegex.test(emailLower)) {
            return {
                isValid: false,
                message: 'Please enter a valid email address (e.g., <EMAIL>)'
            };
        }
        
        // Check for dots at start/end or consecutive dots
        if (emailLower.startsWith('.') || emailLower.endsWith('.') || emailLower.includes('..')) {
            return {
                isValid: false,
                message: 'Email cannot start/end with dots or contain consecutive dots'
            };
        }
        
        // Check domain validity
        const domain = emailLower.split('@')[1];
        if (!domain || domain.length < 3 || !domain.includes('.')) {
            return {
                isValid: false,
                message: 'Please enter a valid email domain'
            };
        }
        
        // Check for common typos
        const commonDomains = {
            'gmail.co': 'gmail.com',
            'gmail.cm': 'gmail.com',
            'gmial.com': 'gmail.com',
            'yahoo.co': 'yahoo.com',
            'yahoo.cm': 'yahoo.com',
            'hotmail.co': 'hotmail.com',
            'hotmail.cm': 'hotmail.com',
            'outlook.co': 'outlook.com',
            'outlook.cm': 'outlook.com'
        };
        
        if (commonDomains[domain]) {
            return {
                isValid: true,
                suggestion: `${emailLower.split('@')[0]}@${commonDomains[domain]}`
            };
        }
        
        return { isValid: true };
    }
    
    function showError(input, message) {
        input.classList.add('is-invalid');
        input.nextElementSibling.textContent = message;
    }
    
    function clearErrors() {
        Object.values(formInputs).forEach(input => {
            input.classList.remove('is-invalid');
            input.nextElementSibling.textContent = '';
        });
    }
    
    function resetForm() {
        contactForm.reset();
        clearErrors();
        setSubmitLoading(false);
    }
    
    function setSubmitLoading(loading) {
        const btnText = submitBtn.querySelector('.btn-text');
        const btnSpinner = submitBtn.querySelector('.btn-spinner');
        
        if (loading) {
            btnText.classList.add('d-none');
            btnSpinner.classList.remove('d-none');
            submitBtn.disabled = true;
        } else {
            btnText.classList.remove('d-none');
            btnSpinner.classList.add('d-none');
            submitBtn.disabled = false;
        }
    }
    
    function showSuccessToast() {
        const toast = new bootstrap.Toast(successToast);
        toast.show();
    }
});