// FAQ Component JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Email form functionality
    const emailInput = document.getElementById('emailInput');
    const submitBtn = document.getElementById('submitBtn');
    const emailError = document.getElementById('emailError');
    const btnText = submitBtn.querySelector('.btn-text');
    const spinner = submitBtn.querySelector('.spinner-border');
    const arrowIcon = submitBtn.querySelector('.arrow-icon');
    
    let isLoading = false;

    // Email validation function
    function validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // Show error message
    function showError(message) {
        emailError.querySelector('small').textContent = message;
        emailError.classList.remove('d-none');
        emailInput.classList.add('error');
    }

    // Hide error message
    function hideError() {
        emailError.classList.add('d-none');
        emailInput.classList.remove('error');
    }

    // Show success message (you can customize this)
    function showSuccess(message) {
        // You can implement a toast notification here
        alert(message); // Simple alert for demo
    }

    // Handle email input changes
    emailInput.addEventListener('input', function() {
        if (!emailError.classList.contains('d-none')) {
            hideError();
        }
    });

    // Handle email submission
    submitBtn.addEventListener('click', async function() {
        if (isLoading) return;

        // Clear any previous errors
        hideError();

        // Get and trim email value
        const email = emailInput.value.trim();

        // Validation checks
        if (!email) {
            showError('Please enter your email address');
            return;
        }

        if (!validateEmail(email)) {
            showError('Please enter a valid email address');
            return;
        }

        if (email.length > 254) {
            showError('Email address is too long');
            return;
        }

        // Start loading state
        isLoading = true;
        submitBtn.disabled = true;
        emailInput.disabled = true;
        
        // Show loading UI
        btnText.textContent = 'Processing...';
        spinner.classList.remove('d-none');
        arrowIcon.classList.add('d-none');

        try {
            // Simulate API call with random delay (1-2 seconds)
            const randomDelay = Math.floor(Math.random() * 1000) + 1000;
            await new Promise(resolve => setTimeout(resolve, randomDelay));

            // Success handling
            console.log('Email submitted:', email);
            showSuccess('Thank you for your interest!');
            emailInput.value = ''; // Clear input
            
        } catch (error) {
            console.error('Error submitting email:', error);
            showError('Something went wrong. Please try again.');
        } finally {
            // Reset loading state
            isLoading = false;
            submitBtn.disabled = false;
            emailInput.disabled = false;
            
            // Reset UI
            btnText.textContent = "Let's Talk";
            spinner.classList.add('d-none');
            arrowIcon.classList.remove('d-none');
        }
    });

    // FAQ Accordion functionality
    const faqButtons = document.querySelectorAll('.faq-button');
    
    faqButtons.forEach(button => {
        button.addEventListener('click', function() {
            const target = this.getAttribute('data-bs-target');
            const collapse = document.querySelector(target);
            const plusIcon = this.querySelector('.plus-icon');
            const minusIcon = this.querySelector('.minus-icon');
            
            // Toggle icons with smooth transition
            if (this.getAttribute('aria-expanded') === 'true') {
                // Currently expanded, will collapse
                setTimeout(() => {
                    plusIcon.classList.remove('d-none');
                    minusIcon.classList.add('d-none');
                }, 150);
            } else {
                // Currently collapsed, will expand
                plusIcon.classList.add('d-none');
                minusIcon.classList.remove('d-none');
            }
        });
    });

    // Handle Bootstrap collapse events for better icon management
    document.querySelectorAll('.faq-collapse').forEach(collapse => {
        collapse.addEventListener('show.bs.collapse', function() {
            const button = document.querySelector(`[data-bs-target="#${this.id}"]`);
            const plusIcon = button.querySelector('.plus-icon');
            const minusIcon = button.querySelector('.minus-icon');
            
            plusIcon.classList.add('d-none');
            minusIcon.classList.remove('d-none');
        });
        
        collapse.addEventListener('hide.bs.collapse', function() {
            const button = document.querySelector(`[data-bs-target="#${this.id}"]`);
            const plusIcon = button.querySelector('.plus-icon');
            const minusIcon = button.querySelector('.minus-icon');
            
            plusIcon.classList.remove('d-none');
            minusIcon.classList.add('d-none');
        });
    });

    // Handle Enter key for email submission
    emailInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            submitBtn.click();
        }
    });
});
