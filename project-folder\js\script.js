// Image preloading and gallery initialization
document.addEventListener('DOMContentLoaded', function() {
    // Image sources for preloading
    const imageSources = [
        '../../assets/hero/Pinterest.svg',
        '../../assets/hero/Dummy-01.avif',
        '../../assets/hero/Instagram.svg',
        '../../assets/hero/Dummy-02.avif',
        '../../assets/hero/Dummy-03.avif',
        '../../assets/hero/LinkedIn.svg',
        '../../assets/hero/Dummy-04.avif',
        '../../assets/hero/Dummy-05.avif'
    ];

    // Cache for loaded images
    const imageCache = new Set();
    let loadedCount = 0;
    const totalImages = imageSources.length;

    // Get skeleton and gallery elements
    const mobileSkeleton = document.querySelector('.mobile-skeleton');
    const mobileGallery = document.querySelector('.mobile-gallery-content');
    const desktopSkeleton = document.querySelector('.desktop-skeleton');
    const desktopGallery = document.querySelector('.desktop-gallery-content');

    // Preload images
    function preloadImages() {
        imageSources.forEach(src => {
            const img = new Image();
            img.crossOrigin = 'anonymous';
            
            img.onload = function() {
                imageCache.add(src);
                loadedCount++;
                
                if (loadedCount === totalImages) {
                    showGalleries();
                }
            };
            
            img.onerror = function() {
                loadedCount++;
                
                if (loadedCount === totalImages) {
                    showGalleries();
                }
            };
            
            img.src = src;
        });

        // Fallback timeout to prevent infinite loading
        setTimeout(() => {
            if (loadedCount < totalImages) {
                showGalleries();
            }
        }, 2000);
    }



    // Intersection Observer for performance optimization
    if ('IntersectionObserver' in window) {
        const galleryObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const scrollElements = entry.target.querySelectorAll('.scroll-content, .scroll-content-vertical');
                    scrollElements.forEach(element => {
                        element.style.animationPlayState = 'running';
                    });
                } else {
                    const scrollElements = entry.target.querySelectorAll('.scroll-content, .scroll-content-vertical');
                    scrollElements.forEach(element => {
                        element.style.animationPlayState = 'paused';
                    });
                }
            });
        }, {
            threshold: 0.1
        });

        // Observe gallery containers
        const galleryContainers = document.querySelectorAll('.mobile-gallery, .desktop-gallery');
        galleryContainers.forEach(container => {
            galleryObserver.observe(container);
        });
    }

    // Handle reduced motion preference
    if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
        const scrollElements = document.querySelectorAll('.scroll-content, .scroll-content-vertical');
        scrollElements.forEach(element => {
            element.style.animation = 'none';
        });
    }

    // Lazy loading for images
    if ('loading' in HTMLImageElement.prototype) {
        const images = document.querySelectorAll('img[loading="lazy"]');
        images.forEach(img => {
            img.loading = 'lazy';
        });
    } else {
        // Fallback for browsers that don't support native lazy loading
        const lazyImages = document.querySelectorAll('.gallery-image-desktop');
        
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        if (img.dataset.src) {
                            img.src = img.dataset.src;
                            img.removeAttribute('data-src');
                        }
                        imageObserver.unobserve(img);
                    }
                });
            });

            lazyImages.forEach(img => {
                imageObserver.observe(img);
            });
        }
    }

    // Handle window resize for responsive behavior
    let resizeTimeout;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(function() {
            // Recalculate animations if needed
            const scrollElements = document.querySelectorAll('.scroll-content, .scroll-content-vertical');
            scrollElements.forEach(element => {
                element.style.animation = 'none';
                element.offsetHeight; // Trigger reflow
                element.style.animation = null;
            });
        }, 250);
    });

    // Error handling for images
    const allImages = document.querySelectorAll('img');
    allImages.forEach(img => {
        img.addEventListener('error', function() {
            // Replace with placeholder or hide if image fails to load
            this.style.opacity = '0.5';
            this.alt = 'Image unavailable';
        });
    });

    // Performance monitoring
    if ('performance' in window && 'mark' in window.performance) {
        window.performance.mark('gallery-init-start');
        
        setTimeout(() => {
            window.performance.mark('gallery-init-end');
            window.performance.measure('gallery-init', 'gallery-init-start', 'gallery-init-end');
        }, 100);
    }
});

// Additional utility functions
function pauseAnimations() {
    const scrollElements = document.querySelectorAll('.scroll-content, .scroll-content-vertical');
    scrollElements.forEach(element => {
        element.style.animationPlayState = 'paused';
    });
}

function resumeAnimations() {
    const scrollElements = document.querySelectorAll('.scroll-content, .scroll-content-vertical');
    scrollElements.forEach(element => {
        element.style.animationPlayState = 'running';
    });
}

// Export functions for potential external use
window.galleryUtils = {
    pauseAnimations,
    resumeAnimations
};