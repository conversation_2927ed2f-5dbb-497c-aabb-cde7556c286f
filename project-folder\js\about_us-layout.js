 fetch('components/header/header.html')
  .then(res => res.text())
  .then(data => {
    document.getElementById('header').innerHTML = data;
  });


  fetch('components/AboutUs/Aboutpage_hero.html')
  .then(res => res.text())
  .then(data => {
    document.getElementById('Aboutpage_hero').innerHTML = data;
  });



  fetch('components/AboutUs/Cards.html')
  .then(res => res.text())
  .then(data => {
    document.getElementById('Cards').innerHTML = data;
  });

  fetch('components/AboutUs/TextAndImage.html')
  .then(res => res.text())
  .then(data => {
    document.getElementById('TextAndImage').innerHTML = data;
  });

  fetch('components/AboutUs/Stats.html')
  .then(res => res.text())
  .then(data => {
    document.getElementById('Stats').innerHTML = data;
  });


  fetch('components/AboutUs/OurMission.html')
  .then(res => res.text())
  .then(data => {
    document.getElementById('OurMission').innerHTML = data;
  });



  fetch('components/AboutUs/GetStarted.html')
  .then(res => res.text())
  .then(data => {
    document.getElementById('GetStarted').innerHTML = data;
  });


 
fetch('components/footer/footer.html')
  .then(res => res.text())
  .then(data => {
    document.getElementById('footer').innerHTML = data;
  });

  