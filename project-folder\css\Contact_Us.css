
/* ------------------------ Contact_Us-hero --------------------- */

/* Custom CSS for Hero Section */

/* Font Family */
body {
    font-family: 'Figtree', sans-serif;
}

/* Hero Container */
.hero-skeleton,
.hero-main {
    margin-top: 120px;
    width: 90%;
    margin-left: auto;
    margin-right: auto;
}

/* Hero Header */
.hero-header,
.skeleton-header {
    background-color: #5a3a32;
    background-image: url('https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Blog_bg-cpDXJ7IDf2Ele8mCcOQgAoE4DZhviu.svg');
    background-size: cover;
    background-position: center;
    border-radius: 12px;
    color: white;
    padding: 1rem 1rem;
    margin-top: 2rem;
    margin-bottom: 2rem;
    overflow: hidden;
    position: relative;
    min-height: 400px;
}

/* Responsive adjustments for hero header */
@media (min-width: 576px) {
    .hero-header,
    .skeleton-header {
        border-radius: 16px;
        padding: 1rem 4rem;
        margin-top: 3rem;
        margin-bottom: 4rem;
    }
}

@media (min-width: 992px) {
    .hero-header,
    .skeleton-header {
        border-radius: 20px;
        margin-top: 160px;
        margin-bottom: 120px;
    }
}

@media (min-width: 1200px) {
    .hero-header,
    .skeleton-header {
        height: 522px;
    }
}

/* Hero Title */
.hero-title {
    font-weight: 600;
    font-size: 30px;
    line-height: 55px;
}

@media (min-width: 768px) {
    .hero-title {
        font-size: 35px;
        line-height: 60px;
    }
}

@media (min-width: 992px) {
    .hero-title {
        font-size: 45px;
        line-height: 90px;
    }
}

/* Circle Decoration */
.circle-decoration {
    position: absolute;
    top: -8px;
    left: -51px;
    width: 270px;
    height: 95px;
}

@media (min-width: 768px) {
    .circle-decoration {
        top: -7px;
    }
}

@media (min-width: 992px) {
    .circle-decoration {
        top: -5px;
        left: -41px;
    }
}

/* Hero Description */
.hero-description {
    margin-top: 30px;
    font-weight: 300;
    font-size: 16px;
    color: rgba(255, 255, 255, 0.6);
}

@media (min-width: 768px) {
    .hero-description {
        font-size: 18px;
    }
}

@media (min-width: 992px) {
    .hero-description {
        font-size: 28px;
    }
}

/* Hero Image */
.hero-image {
    width: 180px;
    height: 220px;
}

@media (min-width: 768px) {
    .hero-image {
        width: 260px;
        height: 320px;
    }
}

@media (min-width: 1200px) {
    .hero-image {
        margin-bottom: 68px;
    }
}



/* Additional responsive utilities */
.hero-main .row {
    min-height: inherit;
}

/* Smooth transitions */
.hero-main,
.hero-skeleton {
    transition: opacity 0.3s ease-in-out;
}

/* Custom spacing for mobile */
@media (max-width: 767px) {
    .hero-header .col-12:first-child {
        padding-bottom: 2rem;
    }
    
    .hero-header .col-12:last-child {
        padding-top: 2rem;
    }
}





























/* ---------------------------- Conversation --------------------------------- */




/* Custom CSS for Conversation Section */

/* Font Family */
body {
    font-family: 'Figtree', sans-serif;
}

/* Color Variables */
:root {
    --primary-brown: #563D39;
    --light-grey: #EFECEC;
    --text-grey: #00000099;
    --brown-light: #563D3933;
    --brown-hover: #4a332f;
}



/* Main Content Styles */
.conversation-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-brown);
}

@media (min-width: 768px) {
    .conversation-title {
        font-size: 1.875rem;
    }
}

.conversation-subtitle {
    color: var(--text-grey);
    font-size: 1rem;
    font-weight: 400;
    margin-bottom: 50px;
}

@media (min-width: 768px) {
    .conversation-subtitle {
        font-size: 1.125rem;
    }
}

@media (min-width: 992px) {
    .conversation-subtitle {
        margin-bottom: 100px;
    }
}

.conversation-card {
    background: var(--light-grey);
    border-radius: 40px;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    overflow: visible;
    padding: 2rem 1rem;
}

@media (min-width: 768px) {
    .conversation-card {
        padding: 2rem;
    }
}

.character-section {
    position: relative;
    margin-bottom: 2rem;
}

@media (min-width: 768px) {
    .character-section {
        margin-bottom: 0;
    }
}

.character-container {
    position: relative;
    width: 320px;
    height: 370px;
    background: var(--primary-brown);
    background-image: url('https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Blog_bg-dWNUUN3ym5hdd4CsBvFWkOR0W69yxA.svg');
    background-size: cover;
    background-position: center;
    border-radius: 40px;
    z-index: 10;
}

@media (min-width: 992px) {
    .character-container {
        width: 440px;
        height: 460px;
    }
}

@media (min-width: 1200px) {
    .character-container {
        margin-left: -4rem;
    }
}

.character-image {
    position: absolute;
    left: 0;
    top: -72px;
    width: 180px;
    height: auto;
}

@media (min-width: 992px) {
    .character-image {
        top: -147px;
        width: 260px;
    }
}

.conversation-content {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    min-height: 460px;
}

@media (min-width: 768px) {
    .conversation-content {
        align-items: flex-start;
        text-align: left;
        padding-left: 3rem;
        padding-right: 2rem;
    }
}

.vertical-line {
    position: absolute;
    left: 0;
    top: 90px;
    width: 16px;
    height: calc(100% - 180px);
}

.line-brown {
    background: var(--primary-brown);
    width: 2px;
    height: 80px;
    border-radius: 2px;
    margin-left: 20px;
}

.line-grey {
    background: var(--brown-light);
    width: 2px;
    height: calc(100% - 230px);
    border-radius: 2px;
    margin-left: 20px;
}

.content-wrapper {
    position: relative;
    z-index: 1;
}

.conversation-tag {
    font-size: 0.875rem;
    color: var(--primary-brown);
    font-weight: 500;
}

@media (min-width: 992px) {
    .conversation-tag {
        font-size: 1rem;
    }
}

.conversation-heading {
    font-size: 1.875rem;
    font-weight: 400;
    color: var(--primary-brown);
    line-height: 1.2;
}

@media (min-width: 992px) {
    .conversation-heading {
        font-size: 3.75rem;
    }
}

.conversation-btn {
    background: var(--primary-brown);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 24px 8px 24px;
    width: 200px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 1rem;
    font-weight: 400;
    transition: background-color 0.3s ease;
}

.conversation-btn:hover {
    background: var(--brown-hover);
    color: white;
}

.btn-arrow {
    width: 34px;
    height: 34px;
}

.conversation-taglines {
    color: var(--brown-light);
    font-size: 1.25rem;
    font-weight: 400;
    line-height: 1.6;
}

@media (min-width: 992px) {
    .conversation-taglines {
        font-size: 2.25rem;
    }
}

.conversation-taglines div {
    margin-bottom: 0.5rem;
}

/* Modal Styles */
.contact-modal {
    border-radius: 16px;
    border: none;
}

.contact-modal-title {
    color: var(--primary-brown);
    font-weight: 600;
    font-size: 1.5rem;
}

.contact-modal-close {
    color: var(--primary-brown);
    opacity: 1;
}

.contact-modal-close:hover {
    background-color: rgba(86, 61, 57, 0.1);
}

.contact-modal-subtitle {
    color: var(--text-grey);
    font-size: 0.95rem;
}

.contact-input {
    border-radius: 8px;
    border: 1px solid #ced4da;
    font-family: 'Figtree', sans-serif;
    transition: border-color 0.3s ease;
}

.contact-input:focus {
    border-color: var(--primary-brown);
    box-shadow: 0 0 0 0.2rem rgba(86, 61, 57, 0.25);
}

.contact-input:hover {
    border-color: var(--primary-brown);
}

.form-label {
    color: #212529;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.invalid-feedback {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

.contact-cancel-btn {
    color: var(--primary-brown);
    background: transparent;
    border: none;
    font-family: 'Figtree', sans-serif;
}

.contact-cancel-btn:hover {
    background-color: rgba(86, 61, 57, 0.1);
    color: var(--primary-brown);
}

.contact-cancel-btn:disabled {
    color: rgba(86, 61, 57, 0.4);
}

.contact-submit-btn {
    background: var(--primary-brown);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 0.5rem 1.5rem;
    font-family: 'Figtree', sans-serif;
    font-weight: 500;
    text-transform: none;
    min-width: 140px;
    transition: background-color 0.3s ease;
}

.contact-submit-btn:hover {
    background: var(--brown-hover);
    color: white;
}

.contact-submit-btn:disabled {
    background: rgba(86, 61, 57, 0.4);
    color: rgba(255, 255, 255, 0.6);
}

/* Responsive adjustments */
@media (max-width: 767px) {
    .conversation-card {
        padding: 2rem 1rem;
    }
    
    .character-container {
        width: 280px;
        height: 320px;
    }
    
    .character-image {
        top: -60px;
        width: 160px;
    }
    
    .conversation-heading {
        font-size: 2rem;
    }
    
    .conversation-taglines {
        font-size: 1.125rem;
    }
}

/* Animation for smooth transitions */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Toast customization */
.toast-header.bg-success {
    background-color: #198754 !important;
}



















/* ------------------------------- Features ------------------------------- */




/* Custom CSS for Features Section */

/* Font Family */
body {
    font-family: 'Figtree', sans-serif;
}

/* Features Container */
.features-container {
    background-color: #563D39;
    color: white;
    width: 90%;
    max-width: 1200px;
    border-radius: 2rem;
    padding: 1.5rem 1rem;
}

/* Responsive padding for features container */
@media (min-width: 576px) {
    .features-container {
        padding: 2rem 1.5rem;
        border-radius: 3rem;
    }
}

@media (min-width: 768px) {
    .features-container {
        padding: 2.5rem 2rem;
    }
}

@media (min-width: 992px) {
    .features-container {
        padding: 3rem 3rem;
        border-radius: 40px;
    }
}

@media (min-width: 1200px) {
    .features-container {
        padding: 3rem 4rem;
    }
}

/* Section padding */
.features-skeleton,
.features-main {
    padding-top: 3rem;
    padding-bottom: 3rem;
}

@media (min-width: 576px) {
    .features-skeleton,
    .features-main {
        padding-top: 4rem;
        padding-bottom: 4rem;
    }
}

@media (min-width: 768px) {
    .features-skeleton,
    .features-main {
        padding-top: 5rem;
        padding-bottom: 5rem;
    }
}

@media (min-width: 992px) {
    .features-skeleton,
    .features-main {
        padding-top: 7.5rem;
        padding-bottom: 7.5rem;
    }
}

/* Features Title */
.features-title {
    font-size: 1.25rem;
    font-weight: 300;
    line-height: 1.6;
    letter-spacing: 0.025em;
}

@media (min-width: 576px) {
    .features-title {
        font-size: 1.5rem;
    }
}

@media (min-width: 768px) {
    .features-title {
        font-size: 1.875rem;
    }
}

@media (min-width: 992px) {
    .features-title {
        font-size: 1.875rem;
    }
}

/* Feature Cards */
.feature-card {
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 0.75rem;
    padding: 1rem;
    text-align: left;
}

@media (min-width: 576px) {
    .feature-card {
        border-radius: 1rem;
        padding: 1.5rem;
    }
}

@media (min-width: 768px) {
    .feature-card {
        padding: 2rem;
    }
}

@media (min-width: 992px) {
    .feature-card {
        border-radius: 18px;
        padding: 1.875rem;
    }
}

/* Feature Card Title */
.feature-card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: white;
    line-height: 1.25;
    margin-bottom: 0.75rem;
}

@media (min-width: 576px) {
    .feature-card-title {
        font-size: 1.25rem;
        margin-bottom: 1rem;
    }
}

@media (min-width: 768px) {
    .feature-card-title {
        font-size: 1.25rem;
        margin-bottom: 1.25rem;
    }
}

@media (min-width: 992px) {
    .feature-card-title {
        font-size: 1.25rem;
        margin-bottom: 1.5rem;
    }
}

/* Feature Card Description */
.feature-card-description {
    font-size: 0.875rem;
    font-weight: 200;
    color: #FFFFFF;
    line-height: 1.6;
}

@media (min-width: 576px) {
    .feature-card-description {
        font-size: 1rem;
        line-height: 1.5;
    }
}

@media (min-width: 768px) {
    .feature-card-description {
        font-size: 1rem;
    }
}

@media (min-width: 992px) {
    .feature-card-description {
        font-size: 1rem;
    }
}

